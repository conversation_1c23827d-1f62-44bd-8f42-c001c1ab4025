# Aurum Splendet in Aeternum: Emas yang Bersinar Abadi

Aplikasi web interaktif yang indah dengan tema Studio Ghibli untuk menyatakan perasaan romantis kepada Arum Savitri Chairunisa. Proyek ini menggabungkan elemen matcha, lang<PERSON>, dan budaya Yogyakarta dalam pengalaman bercerita yang magis.

**Pesan Khusus:** Aplikasi ini dibuat dengan sepenuh hati untuk menyampaikan perasaan tulus yang telah terpendam sejak masa SMA, dari seseorang yang selalu mengagumi senyum dan kepribadian Arum.

## 🌟 Features

### Interactive Storytelling
- **5 Beautiful Scenes**: From high school memories to romantic declaration
- **Smooth Transitions**: Seamless scene changes with elegant animations
- **Interactive Elements**: Clickable clouds, leaves, stars, and characters
- **Particle Effects**: Golden particles and magical floating elements

### Technical Features
- **Responsive Design**: Optimized for both desktop and mobile devices
- **Touch Support**: Swipe gestures for mobile navigation
- **Keyboard Navigation**: Arrow keys and spacebar for scene control
- **Audio Controls**: Background music with toggle functionality
- **Day/Night Mode**: Toggle between different visual themes

### Visual Style
- **Studio Ghibli Aesthetic**: Inspired by the magical worlds of Miyazaki films
- **Yogyakarta Elements**: Local landmarks like Mount Merapi and Borobudur
- **Pastel Color Palette**: Soft colors with elegant gold accents
- **Smooth Animations**: CSS3 animations for enhanced user experience

## 🎭 Alur Cerita

1. **Scene Pembukaan**: Langit senja dengan Gunung Merapi, kotak kayu melayang dengan pesan cinta abadi untuk Arum Savitri Chairunisa
2. **Kenangan SMA**: Kelas terpisah (MIA I dan MIA III), daun berguguran dengan teks romantis tentang ketertarikan sejak dulu
3. **Matcha & Langit**: Kebun teh di Yogyakarta dengan karakter Ghibli, pesan tentang kehangatan dan kenyamanan
4. **Deklarasi**: Scene malam dengan Candi Borobudur, bintang jatuh, dan surat terbang berisi pengakuan perasaan yang tulus
5. **Penutup**: Arum berjalan di jalan Yogyakarta dengan burung-burung, pesan harapan dan tekad untuk menjadi yang terbaik

## 🚀 Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Local web server (optional but recommended)

### Installation
1. Clone or download this repository
2. Add background music files to the `audio/` directory (see audio/README.md)
3. Open `index.html` in your web browser

### For Best Experience
- Use a local web server to avoid CORS issues with audio files
- Enable audio autoplay in your browser settings
- Use fullscreen mode for immersive experience

## 🎵 Musik Latar Belakang

✅ **Musik sudah tersedia!** File `seasidetown.mp3` sudah ada di direktori `audio/` dan siap digunakan.

Musik latar akan otomatis dimuat dan dapat dikontrol menggunakan tombol 🎵 di pojok kanan atas aplikasi.

## 🎮 Controls

### Desktop
- **Next Scene**: Click "Lanjutkan" button, Right Arrow key, or Spacebar
- **Previous Scene**: Left Arrow key
- **Restart**: Click "Mulai Lagi" button or press 'R'
- **Music Toggle**: Click 🎵 button
- **Day/Night Mode**: Click 🌙/☀️ button

### Mobile
- **Next Scene**: Tap "Lanjutkan" button or swipe left
- **Previous Scene**: Swipe right
- **Interactive Elements**: Tap clouds, leaves, stars, plants, and birds

## 🎨 Customization

### Colors
Edit `styles.css` to change the color scheme:
- Main gradients are defined in scene backgrounds
- Gold accents use `#FFD700`
- Text colors can be modified in respective text classes

### Text Content
Modify the romantic messages in `index.html`:
- Opening message in Scene 0
- Memory text in Scene 1
- Matcha text in Scene 2
- Declaration letter in Scene 3
- Closing message in Scene 4

### Animations
Adjust animation timings and effects in `styles.css`:
- Duration: Change `animation-duration` values
- Easing: Modify `ease-in-out`, `ease-out` functions
- Delays: Adjust `animation-delay` for sequencing

## 📱 Browser Compatibility

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## 🛠️ Technical Stack

- **HTML5**: Semantic structure and audio support
- **CSS3**: Advanced animations, gradients, and responsive design
- **Vanilla JavaScript**: Interactive functionality and scene management
- **Google Fonts**: Poppins and Dancing Script typography

## 📂 Project Structure

```
Aurum Splendet in Aeternum/
├── index.html          # Main HTML structure
├── styles.css          # Complete styling and animations
├── script.js           # Interactive functionality
├── audio/              # Background music directory
│   └── README.md       # Audio setup instructions
└── README.md           # This file
```

## 💝 Romantic Elements

This application is designed to express deep, sincere feelings through:
- **Nostalgic References**: High school memories and shared experiences
- **Cultural Connection**: Yogyakarta landmarks and Indonesian elements
- **Gentle Metaphors**: Matcha warmth, sky comfort, eternal gold
- **Interactive Magic**: User engagement through clickable elements
- **Beautiful Aesthetics**: Studio Ghibli-inspired visual storytelling

## 🌸 Message of Love

*"Aurum Splendet in Aeternum"* - Gold that shines eternally, representing the enduring nature of true love that has grown since high school days and continues to shine brightly in the heart.

## 📄 License

This project is created with love for personal use. Feel free to adapt it for your own romantic expressions, but please maintain the spirit of sincere, heartfelt communication.

---

*Made with ❤️ for Arum - May this digital love letter convey the depth of feelings that words alone cannot express.*
