# Audio Files for Aurum Splendet in Aeternum

## Background Music

✅ **Current Audio File:**
- `seasidetown.mp3` - Background music sudah tersedia dan siap digunakan

## File Audio yang Digunakan

Aplikasi ini menggunakan file `seasidetown.mp3` yang sudah ada di direktori ini sebagai musik latar belakang.

## Recommended Music Style

For the best Studio Ghibli-inspired experience, choose music that has:

- Gentle piano melodies (similar to <PERSON>'s compositions)
- Soft, romantic instrumental pieces
- Duration: 3-5 minutes (will loop automatically)
- Volume: Moderate (the app sets volume to 30% automatically)

## Free Music Resources

You can find suitable royalty-free music from:

1. **Freesound.org** - Search for "piano romantic" or "ghibli style"
2. **YouTube Audio Library** - Filter by "Romantic" or "Peaceful" mood
3. **Pixabay Music** - Search for "piano love" or "romantic instrumental"
4. **Incompetech** - <PERSON>'s royalty-free music

## Audio Format Support

The application supports:
- MP3 (primary format)
- OGG (fallback format)
- WAV (if needed)

## File Size Recommendations

- Keep audio files under 5MB for faster loading
- Use 128kbps quality for good balance of quality and file size
- Consider using audio compression tools if files are too large

## Note

If no audio files are present, the music toggle button will still appear but won't play anything. The application will work perfectly without music, but adding it enhances the romantic atmosphere significantly.
