<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aurum Splendet in Aeternum: Emas yang Bersinar Abadi</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&family=Dancing+Script:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Audio Controls -->
    <div class="audio-controls">
        <button id="musicToggle" class="music-btn">🎵</button>
        <button id="dayNightToggle" class="day-night-btn">🌙</button>
    </div>

    <!-- Background Audio -->
    <audio id="backgroundMusic" loop autoplay preload="auto" muted>
        <source src="./audio/seasidetown.mp3" type="audio/mpeg">
    </audio>

    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-container">
            <div class="loading-spinner">
                <div class="spinner-circle"></div>
                <div class="spinner-circle"></div>
                <div class="spinner-circle"></div>
            </div>
            <h2 class="loading-title">Aurum Splendet in Aeternum</h2>
            <p class="loading-text">Memuat musik latar belakang...</p>
            <div class="loading-progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <p class="loading-subtitle">Menyiapkan cerita untuk Arum ✨</p>
            <button id="startButton" class="start-button" style="display: none;">
                🎵 Mulai Cerita dengan Musik 🎵
            </button>
        </div>
    </div>

    <!-- Particle Container -->
    <div id="particles"></div>

    <!-- Scene 0: Opening Scene -->
    <section id="scene0" class="scene active">
        <div class="sky-background">
            <div class="cloud cloud1"></div>
            <div class="cloud cloud2"></div>
            <div class="cloud cloud3"></div>
            <div class="mountain merapi"></div>
        </div>
        <div class="floating-box" id="floatingBox">
            <div class="box-content">
                <p class="opening-text">Aurum Splendet in Aeternum</p>
                <p class="subtitle">Untuk Sang Emas yang Bersinar di Langit Hidupku</p>
                <p class="subtitle-small">Arum Savitri Chairunisa - Sepenggal puisi yang terukir sejak masa sekolah</p>
            </div>
        </div>
        <button class="next-btn" onclick="nextScene()">Buka Kisah Ini</button>
    </section>

    <!-- Scene 1: High School Memories -->
    <section id="scene1" class="scene">
        <div class="school-background">
            <div class="classroom mia1">
                <div class="door">
                    <div class="silhouette arum-silhouette"></div>
                </div>
                <div class="class-label">MIA I</div>
            </div>
            <div class="corridor"></div>
            <div class="classroom mia3">
                <div class="door">
                    <div class="silhouette narrator-silhouette"></div>
                </div>
                <div class="class-label">MIA III</div>
            </div>
        </div>
        <div class="falling-leaves">
            <div class="leaf leaf1"></div>
            <div class="leaf leaf2"></div>
            <div class="leaf leaf3"></div>
            <div class="leaf leaf4"></div>
        </div>
        <div class="memory-text">
            <p class="main-text">Sejak dulu di SMA, aku selalu tertarik dengan senyumanmu</p>
            <p class="sub-text">Dulu kau di MIA I, aku di MIA III</p>
            <p class="sub-text">Tapi senyummu selalu menyinari hari-hariku</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Lanjutkan</button>
    </section>

    <!-- Scene 2: Matcha & Sky -->
    <section id="scene2" class="scene">
        <div class="tea-garden">
            <div class="hills"></div>
            <div class="tea-plants">
                <div class="plant plant1"></div>
                <div class="plant plant2"></div>
                <div class="plant plant3"></div>
            </div>
            <div class="ghibli-character">
                <div class="character-body"></div>
                <div class="matcha-cup"></div>
            </div>
        </div>
        <div class="matcha-text">
            <p class="main-text">Seperti matcha, kepribadianmu hangat dan menenangkan</p>
            <p class="sub-text">Seperti langit Jogja, kau selalu jadi tempat yang nyaman</p>
            <p class="sub-text">Aku suka sama kamu, Arum</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Lanjutkan</button>
    </section>

    <!-- Scene 3: Declaration -->
    <section id="scene3" class="scene">
        <div class="night-sky">
            <div class="stars">
                <div class="star star1" onclick="createStarParticles(event)"></div>
                <div class="star star2" onclick="createStarParticles(event)"></div>
                <div class="star star3" onclick="createStarParticles(event)"></div>
                <div class="star star4" onclick="createStarParticles(event)"></div>
                <div class="star star5" onclick="createStarParticles(event)"></div>
            </div>
            <div class="borobudur"></div>
            <div class="flying-letter" id="flyingLetter">
                <div class="letter-content">
                    <p class="letter-title">Untuk Arum Savitri Chairunisa</p>
                    <p class="main-text">Aku ungkapkan rasa suka ku ini karena lebih baik kunyatakan daripada di kemudian hari menyesal tidak pernah kuungkapkan</p>
                    <p class="main-text">Setidaknya aku ungkapkan agar engkau tahu, ada seseorang yang suka sama kamu</p>
                    <p class="main-text">Aku hanya ingin menyatakannya saja, tanpa jawaban pun tak mengapa, karena dengan ini aku jadi merasa lega</p>
                </div>
            </div>
        </div>
        <div class="golden-text">Aurum Splendet in Aeternum</div>
        <button class="next-btn" onclick="nextScene()">Lanjutkan</button>
    </section>

    <!-- Scene 4: Closing -->
    <section id="scene4" class="scene">
        <div class="jogja-path">
            <div class="path"></div>
            <div class="arum-character">
                <div class="character-walking"></div>
            </div>
            <div class="birds">
                <div class="bird bird1"></div>
                <div class="bird bird2"></div>
                <div class="bird bird3"></div>
            </div>
        </div>
        <div class="closing-text">
            <p class="main-text">Satu yang kuharap setelah aku confess sekarang:</p>
            <p class="main-text">Mohon dengan sangat untuk tidak menjadi asing</p>
            <p class="sub-text">Walaupun di kemudian hari aku bukan menjadi takdirmu nanti, aku ingin menjadi temanmu saja</p>
            <p class="sub-text">Menjadi seorang pengawal yang akan mengantarkan tuan putrinya ke pangeran sejatinya</p>
            <p class="hope-text">Janganlah di kemudian hari ini menjadi asing</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Pesan Terakhir</button>
    </section>

    <!-- Scene 5: Final Message -->
    <section id="scene5" class="scene">
        <div class="final-background">
            <div class="gentle-light"></div>
            <div class="floating-hearts">
                <div class="heart heart1">💛</div>
                <div class="heart heart2">🤍</div>
                <div class="heart heart3">💛</div>
            </div>
        </div>
        <div class="final-message">
            <p class="final-title">Pesan Terakhir</p>
            <p class="main-text">Setelah aku confess sekarang, yang paling kuharapkan:</p>
            <p class="main-text">Janganlah di kemudian hari ini menjadi asing</p>
            <p class="sub-text">Aku tahu mungkin ini mengejutkan, mungkin membuat canggung</p>
            <p class="sub-text">Tapi aku berharap persahabatan kita tetap bisa berlanjut</p>
            <p class="hope-text">Karena kehilangan Arum sebagai teman akan lebih menyakitkan daripada tidak dibalas perasaannya</p>
            <p class="gratitude-text">Terima kasih sudah menjadi bagian indah dalam hidupku, Arum</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Lanjutkan Perjalanan</button>
    </section>

    <!-- Scene 6: The Journey Begins -->
    <section id="scene6" class="scene">
        <div class="journey-background">
            <div class="path-forward"></div>
            <div class="mountains-distance"></div>
        </div>
        <div class="journey-text">
            <p class="chapter-title">Bab I: Perjalanan Dimulai</p>
            <p class="main-text">Setelah kata-kata itu terbang dari bibir menuju langit</p>
            <p class="main-text">Aku memulai perjalanan panjang memantaskan diri</p>
            <p class="sub-text">Bukan untuk mengubah takdir, tapi untuk menjadi versi terbaik dari diriku</p>
            <p class="sub-text">Yang layak berdiri di sampingmu, walau hanya sebagai sahabat</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Mulai Perjalanan</button>
    </section>

    <!-- Scene 7: Learning and Growing -->
    <section id="scene7" class="scene">
        <div class="growth-background">
            <div class="books-stack"></div>
            <div class="growing-tree"></div>
            <div class="floating-knowledge"></div>
        </div>
        <div class="growth-text">
            <p class="chapter-title">Bab II: Menumbuhkan Akar</p>
            <p class="main-text">Aku belajar membaca buku-buku yang kau sukai</p>
            <p class="main-text">Menyelami dunia yang membuatmu tertawa</p>
            <p class="sub-text">Setiap halaman adalah jembatan menuju pemahamanmu</p>
            <p class="sub-text">Setiap kata adalah tangga menuju kedewasaan</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Terus Belajar</button>
    </section>

    <!-- Scene 8: Building Character -->
    <section id="scene8" class="scene">
        <div class="character-background">
            <div class="mirror-reflection"></div>
            <div class="inner-light"></div>
        </div>
        <div class="character-text">
            <p class="chapter-title">Bab III: Membangun Karakter</p>
            <p class="main-text">Aku belajar mendengarkan lebih dari berbicara</p>
            <p class="main-text">Memahami bahwa cinta sejati adalah memberi tanpa mengharap</p>
            <p class="sub-text">Setiap hari adalah latihan menjadi manusia yang lebih baik</p>
            <p class="sub-text">Yang bisa dipercaya, diandalkan, dan membawa kedamaian</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Terus Berproses</button>
    </section>

    <!-- Scene 9: Facing Challenges -->
    <section id="scene9" class="scene">
        <div class="challenge-background">
            <div class="storm-clouds"></div>
            <div class="lightning"></div>
            <div class="standing-figure"></div>
        </div>
        <div class="challenge-text">
            <p class="chapter-title">Bab IV: Menghadapi Badai</p>
            <p class="main-text">Ada hari-hari ketika keraguan menghampiri</p>
            <p class="main-text">"Apakah aku cukup baik? Apakah perjalanan ini sia-sia?"</p>
            <p class="sub-text">Tapi kemudian aku ingat senyummu</p>
            <p class="sub-text">Dan aku bangkit lagi, lebih kuat dari sebelumnya</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Bangkit Lagi</button>
    </section>

    <!-- Scene 10: Finding Purpose -->
    <section id="scene10" class="scene">
        <div class="purpose-background">
            <div class="compass"></div>
            <div class="north-star"></div>
        </div>
        <div class="purpose-text">
            <p class="chapter-title">Bab V: Menemukan Tujuan</p>
            <p class="main-text">Aku menyadari bahwa perjalanan ini bukan hanya untukmu</p>
            <p class="main-text">Tapi juga untuk diriku, untuk menjadi manusia yang utuh</p>
            <p class="sub-text">Yang bisa memberi makna pada setiap langkah</p>
            <p class="sub-text">Yang bisa menjadi cahaya bagi orang-orang di sekitarnya</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Temukan Makna</button>
    </section>

    <!-- Scene 11: Becoming Worthy -->
    <section id="scene11" class="scene">
        <div class="worthy-background">
            <div class="golden-aura"></div>
            <div class="achievement-symbols"></div>
        </div>
        <div class="worthy-text">
            <p class="chapter-title">Bab VI: Menjadi Layak</p>
            <p class="main-text">Perlahan, aku melihat perubahan dalam diriku</p>
            <p class="main-text">Bukan untuk memaksakan cinta, tapi untuk menjadi sahabat terbaik</p>
            <p class="sub-text">Yang bisa kau andalkan dalam suka dan duka</p>
            <p class="sub-text">Yang akan selalu ada tanpa mengharapkan balasan</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Hampir Sampai</button>
    </section>

    <!-- Scene 12: The Promise -->
    <section id="scene12" class="scene">
        <div class="promise-background">
            <div class="eternal-flame"></div>
            <div class="guardian-silhouette"></div>
        </div>
        <div class="promise-text">
            <p class="chapter-title">Bab VII: Janji Abadi</p>
            <p class="main-text">Inilah janjiku padamu, Arum:</p>
            <p class="main-text">Aku akan menjadi pengawal yang setia</p>
            <p class="sub-text">Yang akan menjaga kebahagiaanmu dari kejauhan</p>
            <p class="sub-text">Yang akan merayakan setiap senyummu</p>
            <p class="sub-text">Dan yang akan selalu mendoakanmu dalam setiap langkah</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Janji Terakhir</button>
    </section>

    <!-- Scene 13: Final Dedication -->
    <section id="scene13" class="scene">
        <div class="dedication-background">
            <div class="infinite-stars"></div>
            <div class="golden-inscription"></div>
        </div>
        <div class="dedication-text">
            <p class="final-title">Epilog: Aurum Splendet in Aeternum</p>
            <p class="main-text">Perjalanan ini mengajarkanku bahwa cinta sejati</p>
            <p class="main-text">Bukan tentang memiliki, tapi tentang memberi</p>
            <p class="main-text">Bukan tentang mengubah takdir, tapi tentang menerima dengan lapang dada</p>
            <p class="gratitude-text">Terima kasih telah menjadi alasan aku menjadi manusia yang lebih baik</p>
            <p class="hope-text">Aurum Splendet in Aeternum - Emas yang bersinar selamanya dalam kenangan</p>
        </div>
        <button class="restart-btn" onclick="restartStory()">Mulai Dari Awal</button>
    </section>

    <script src="script.js"></script>
</body>
</html>
