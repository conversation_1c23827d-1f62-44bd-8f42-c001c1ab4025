<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aurum Splendet in Aeternum: Emas yang Bersinar Abadi</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&family=Dancing+Script:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Audio Controls -->
    <div class="audio-controls">
        <button id="musicToggle" class="music-btn">🎵</button>
        <button id="dayNightToggle" class="day-night-btn">🌙</button>
    </div>

    <!-- Background Audio -->
    <audio id="backgroundMusic" loop autoplay preload="auto" muted>
        <source src="./audio/seasidetown.mp3" type="audio/mpeg">
    </audio>

    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-container">
            <div class="loading-spinner">
                <div class="spinner-circle"></div>
                <div class="spinner-circle"></div>
                <div class="spinner-circle"></div>
            </div>
            <h2 class="loading-title">Aurum Splendet in Aeternum</h2>
            <p class="loading-text">Memuat musik latar belakang...</p>
            <div class="loading-progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <p class="loading-subtitle">Menyiapkan cerita untuk Arum ✨</p>
            <button id="startButton" class="start-button" style="display: none;">
                🎵 Mulai Cerita dengan Musik 🎵
            </button>
        </div>
    </div>

    <!-- Particle Container -->
    <div id="particles"></div>

    <!-- Scene 0: Opening Scene -->
    <section id="scene0" class="scene active">
        <div class="sky-background">
            <div class="cloud cloud1"></div>
            <div class="cloud cloud2"></div>
            <div class="cloud cloud3"></div>
            <div class="mountain merapi"></div>
        </div>
        <div class="floating-box" id="floatingBox">
            <div class="box-content">
                <p class="opening-text">Aurum Splendet in Aeternum</p>
                <p class="subtitle">Untuk Arum Savitri Chairunisa</p>
                <p class="subtitle-small">Emas yang kusuka sejak SMA, bersinar abadi dalam hati</p>
            </div>
        </div>
        <button class="next-btn" onclick="nextScene()">Mulai Cerita</button>
    </section>

    <!-- Scene 1: High School Memories -->
    <section id="scene1" class="scene">
        <div class="school-background">
            <div class="classroom mia1">
                <div class="door">
                    <div class="silhouette arum-silhouette"></div>
                </div>
                <div class="class-label">MIA I</div>
            </div>
            <div class="corridor"></div>
            <div class="classroom mia3">
                <div class="door">
                    <div class="silhouette narrator-silhouette"></div>
                </div>
                <div class="class-label">MIA III</div>
            </div>
        </div>
        <div class="falling-leaves">
            <div class="leaf leaf1"></div>
            <div class="leaf leaf2"></div>
            <div class="leaf leaf3"></div>
            <div class="leaf leaf4"></div>
        </div>
        <div class="memory-text">
            <p class="main-text">Sejak dulu di SMA, aku selalu tertarik dengan senyumanmu</p>
            <p class="sub-text">Dulu kau di MIA I, aku di MIA III</p>
            <p class="sub-text">Tapi senyummu selalu menyinari hari-hariku</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Lanjutkan</button>
    </section>

    <!-- Scene 2: Matcha & Sky -->
    <section id="scene2" class="scene">
        <div class="tea-garden">
            <div class="hills"></div>
            <div class="tea-plants">
                <div class="plant plant1"></div>
                <div class="plant plant2"></div>
                <div class="plant plant3"></div>
            </div>
            <div class="ghibli-character">
                <div class="character-body"></div>
                <div class="matcha-cup"></div>
            </div>
        </div>
        <div class="matcha-text">
            <p class="main-text">Seperti matcha, kepribadianmu hangat dan menenangkan</p>
            <p class="sub-text">Seperti langit Jogja, kau selalu jadi tempat yang nyaman</p>
            <p class="sub-text">Aku suka sama kamu, Arum</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Lanjutkan</button>
    </section>

    <!-- Scene 3: Declaration -->
    <section id="scene3" class="scene">
        <div class="night-sky">
            <div class="stars">
                <div class="star star1" onclick="createStarParticles(event)"></div>
                <div class="star star2" onclick="createStarParticles(event)"></div>
                <div class="star star3" onclick="createStarParticles(event)"></div>
                <div class="star star4" onclick="createStarParticles(event)"></div>
                <div class="star star5" onclick="createStarParticles(event)"></div>
            </div>
            <div class="borobudur"></div>
            <div class="flying-letter" id="flyingLetter">
                <div class="letter-content">
                    <p class="letter-title">Untuk Arum Savitri Chairunisa</p>
                    <p class="main-text">Aku ungkapkan rasa suka ku ini karena lebih baik kunyatakan daripada di kemudian hari menyesal tidak pernah kuungkapkan</p>
                    <p class="main-text">Setidaknya aku ungkapkan agar engkau tahu, ada seseorang yang suka sama kamu</p>
                    <p class="main-text">Aku hanya ingin menyatakannya saja, tanpa jawaban pun tak mengapa, karena dengan ini aku jadi merasa lega</p>
                </div>
            </div>
        </div>
        <div class="golden-text">Aurum Splendet in Aeternum</div>
        <button class="next-btn" onclick="nextScene()">Lanjutkan</button>
    </section>

    <!-- Scene 4: Closing -->
    <section id="scene4" class="scene">
        <div class="jogja-path">
            <div class="path"></div>
            <div class="arum-character">
                <div class="character-walking"></div>
            </div>
            <div class="birds">
                <div class="bird bird1"></div>
                <div class="bird bird2"></div>
                <div class="bird bird3"></div>
            </div>
        </div>
        <div class="closing-text">
            <p class="main-text">Satu yang kuharap setelah aku confess sekarang:</p>
            <p class="main-text">Mohon dengan sangat untuk tidak menjadi asing</p>
            <p class="sub-text">Walaupun di kemudian hari aku bukan menjadi takdirmu nanti, aku ingin menjadi temanmu saja</p>
            <p class="sub-text">Menjadi seorang pengawal yang akan mengantarkan tuan putrinya ke pangeran sejatinya</p>
            <p class="hope-text">Janganlah di kemudian hari ini menjadi asing</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Pesan Terakhir</button>
    </section>

    <!-- Scene 5: Final Message -->
    <section id="scene5" class="scene">
        <div class="final-background">
            <div class="gentle-light"></div>
            <div class="floating-hearts">
                <div class="heart heart1">💛</div>
                <div class="heart heart2">🤍</div>
                <div class="heart heart3">💛</div>
            </div>
        </div>
        <div class="final-message">
            <p class="final-title">Pesan Terakhir</p>
            <p class="main-text">Setelah aku confess sekarang, yang paling kuharapkan:</p>
            <p class="main-text">Janganlah di kemudian hari ini menjadi asing</p>
            <p class="sub-text">Aku tahu mungkin ini mengejutkan, mungkin membuat canggung</p>
            <p class="sub-text">Tapi aku berharap persahabatan kita tetap bisa berlanjut</p>
            <p class="hope-text">Karena kehilangan Arum sebagai teman akan lebih menyakitkan daripada tidak dibalas perasaannya</p>
            <p class="gratitude-text">Terima kasih sudah menjadi bagian indah dalam hidupku, Arum</p>
        </div>
        <button class="restart-btn" onclick="restartStory()">Mulai Lagi</button>
    </section>

    <script src="script.js"></script>
</body>
</html>
