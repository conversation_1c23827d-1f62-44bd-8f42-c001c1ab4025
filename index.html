<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aurum Splendet in Aeternum: Emas yang Bersinar Abadi</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&family=Dancing+Script:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Audio Controls -->
    <div class="audio-controls">
        <button id="musicToggle" class="music-btn">🎵</button>
        <button id="dayNightToggle" class="day-night-btn">🌙</button>
    </div>

    <!-- Background Audio -->
    <audio id="backgroundMusic" loop>
        <source src="audio/seasidetown.mp3" type="audio/mpeg">
    </audio>

    <!-- Particle Container -->
    <div id="particles"></div>

    <!-- Scene 0: Opening Scene -->
    <section id="scene0" class="scene active">
        <div class="sky-background">
            <div class="cloud cloud1"></div>
            <div class="cloud cloud2"></div>
            <div class="cloud cloud3"></div>
            <div class="mountain merapi"></div>
        </div>
        <div class="floating-box" id="floatingBox">
            <div class="box-content">
                <p class="opening-text">Aurum Splendet in Aeternum</p>
                <p class="subtitle">Untuk Arum Savitri Chairunisa</p>
                <p class="subtitle-small">Emas yang kusuka sejak SMA, bersinar abadi dalam hati</p>
            </div>
        </div>
        <button class="next-btn" onclick="nextScene()">Mulai Cerita</button>
    </section>

    <!-- Scene 1: High School Memories -->
    <section id="scene1" class="scene">
        <div class="school-background">
            <div class="classroom mia1">
                <div class="door">
                    <div class="silhouette arum-silhouette"></div>
                </div>
                <div class="class-label">MIA I</div>
            </div>
            <div class="corridor"></div>
            <div class="classroom mia3">
                <div class="door">
                    <div class="silhouette narrator-silhouette"></div>
                </div>
                <div class="class-label">MIA III</div>
            </div>
        </div>
        <div class="falling-leaves">
            <div class="leaf leaf1"></div>
            <div class="leaf leaf2"></div>
            <div class="leaf leaf3"></div>
            <div class="leaf leaf4"></div>
        </div>
        <div class="memory-text">
            <p class="main-text">Sejak SMA, aku selalu tertarik dengan senyumanmu dan kepribadianmu</p>
            <p class="sub-text">Senyummu seperti matahari yang menyinari hari-hariku</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Lanjutkan</button>
    </section>

    <!-- Scene 2: Matcha & Sky -->
    <section id="scene2" class="scene">
        <div class="tea-garden">
            <div class="hills"></div>
            <div class="tea-plants">
                <div class="plant plant1"></div>
                <div class="plant plant2"></div>
                <div class="plant plant3"></div>
            </div>
            <div class="ghibli-character">
                <div class="character-body"></div>
                <div class="matcha-cup"></div>
            </div>
        </div>
        <div class="matcha-text">
            <p class="main-text">Seperti matcha, kepribadianmu hangat dan menenangkan</p>
            <p class="sub-text">Seperti langit Jogja, kau selalu menjadi tempatku bercerita</p>
            <p class="sub-text">Aku telah jatuh hati denganmu, Arum</p>
        </div>
        <button class="next-btn" onclick="nextScene()">Lanjutkan</button>
    </section>

    <!-- Scene 3: Declaration -->
    <section id="scene3" class="scene">
        <div class="night-sky">
            <div class="stars">
                <div class="star star1" onclick="createStarParticles(event)"></div>
                <div class="star star2" onclick="createStarParticles(event)"></div>
                <div class="star star3" onclick="createStarParticles(event)"></div>
                <div class="star star4" onclick="createStarParticles(event)"></div>
                <div class="star star5" onclick="createStarParticles(event)"></div>
            </div>
            <div class="borobudur"></div>
            <div class="flying-letter" id="flyingLetter">
                <div class="letter-content">
                    <p class="letter-title">Untuk Arum Savitri Chairunisa</p>
                    <p class="main-text">Sudah kupertimbangkan dengan panjang, bagiku lebih baik kunyatakan daripada dikemudian hari menyesal tidak pernah kuungkapkan</p>
                    <p class="main-text">Dari sepanjang aku mengenal Arum... aku suka dengan Arum</p>
                    <p class="main-text">Aku ingin mengenalmu lebih dekat lagi, aku ingin berproses memantaskan diri untukmu</p>
                    <p class="sub-text">Setidaknya Arum tahu ada seseorang di bumi ini selain kedua orang tua yang suka dengan Arum</p>
                </div>
            </div>
        </div>
        <div class="golden-text">Aurum Splendet in Aeternum</div>
        <button class="next-btn" onclick="nextScene()">Lanjutkan</button>
    </section>

    <!-- Scene 4: Closing -->
    <section id="scene4" class="scene">
        <div class="jogja-path">
            <div class="path"></div>
            <div class="arum-character">
                <div class="character-walking"></div>
            </div>
            <div class="birds">
                <div class="bird bird1"></div>
                <div class="bird bird2"></div>
                <div class="bird bird3"></div>
            </div>
        </div>
        <div class="closing-text">
            <p class="main-text">Sampai tiba takdir, aku akan berjuang mencapai potensi terbaik dan versi terbaik dariku</p>
            <p class="main-text">Aku akan selalu berjuang menjadi teman bercerita terbaik untukmu</p>
            <p class="sub-text">Walaupun engkau menolak pernyataan suka ku ini, aku berharap janganlah kita menjadi asing</p>
            <p class="hope-text">Kuharap Arum mengerti maksudku ini</p>
        </div>
        <button class="restart-btn" onclick="restartStory()">Mulai Lagi</button>
    </section>

    <script src="script.js"></script>
</body>
</html>
