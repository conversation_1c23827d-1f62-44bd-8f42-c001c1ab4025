/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 1s ease-out, visibility 1s ease-out;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-container {
    text-align: center;
    color: #fff;
    max-width: 400px;
    padding: 20px;
}

.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
    gap: 8px;
}

.spinner-circle {
    width: 12px;
    height: 12px;
    background: #FFD700;
    border-radius: 50%;
    animation: spinnerBounce 1.4s ease-in-out infinite both;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.spinner-circle:nth-child(1) { animation-delay: -0.32s; }
.spinner-circle:nth-child(2) { animation-delay: -0.16s; }
.spinner-circle:nth-child(3) { animation-delay: 0s; }

@keyframes spinnerBounce {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

.loading-title {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    color: #FFD700;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    from {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }
    to {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 215, 0, 0.6);
    }
}

.loading-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1.2rem;
    color: #FFF8DC;
    margin-bottom: 25px;
    font-weight: 500;
    animation: textPulse 2s ease-in-out infinite;
}

@keyframes textPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.loading-progress {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #FFD700, #FFA500, #FFD700);
    background-size: 200% 100%;
    border-radius: 3px;
    width: 0%;
    transition: width 0.3s ease;
    animation: progressShimmer 2s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

@keyframes progressShimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.loading-subtitle {
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    color: #FFF8DC;
    opacity: 0.9;
    font-style: italic;
    animation: subtitleFloat 3s ease-in-out infinite;
}

@keyframes subtitleFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

.start-button {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    border: none;
    border-radius: 25px;
    padding: 15px 30px;
    font-family: 'Poppins', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    color: #8B4513;
    cursor: pointer;
    margin-top: 20px;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
    transition: all 0.3s ease;
    animation: buttonPulse 2s ease-in-out infinite;
}

.start-button:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
}

.start-button:active {
    transform: scale(0.95);
}

@keyframes buttonPulse {
    0%, 100% { 
        box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        transform: scale(1);
    }
    50% { 
        box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
        transform: scale(1.02);
    }
}

body {
    font-family: 'Poppins', sans-serif;
    overflow: hidden;
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
    transition: all 0.5s ease;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

body.night-mode {
    background: linear-gradient(135deg, #2d3436 0%, #636e72 50%, #74b9ff 100%);
}

/* Audio Controls */
.audio-controls {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    gap: 10px;
}

.music-btn, .day-night-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.music-btn:hover, .day-night-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* Particles */
#particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #ffd700;
    border-radius: 50%;
    animation: float 6s infinite ease-in-out;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 0.7; }
}

/* Scene Base Styles */
.scene {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transform: translateX(100%);
    transition: all 1s ease-in-out;
    padding: 20px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.scene.active {
    opacity: 1;
    transform: translateX(0);
}

/* Scene 0: Opening */
#scene0 {
    background: linear-gradient(to bottom, #87CEEB 0%, #FFE4B5 50%, #98FB98 100%);
}

.sky-background {
    position: absolute;
    width: 100%;
    height: 100%;
}

.cloud {
    position: absolute;
    background: white;
    border-radius: 50px;
    opacity: 0.8;
    animation: cloudFloat 20s infinite ease-in-out;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.cloud:hover {
    transform: scale(1.1);
}

.cloud1 {
    width: 100px;
    height: 40px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.cloud2 {
    width: 150px;
    height: 60px;
    top: 15%;
    right: 20%;
    animation-delay: -5s;
}

.cloud3 {
    width: 80px;
    height: 30px;
    top: 30%;
    left: 60%;
    animation-delay: -10s;
}

@keyframes cloudFloat {
    0%, 100% { transform: translateX(0px); }
    50% { transform: translateX(30px); }
}

.mountain.merapi {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 200px solid transparent;
    border-right: 200px solid transparent;
    border-bottom: 150px solid #8B4513;
    opacity: 0.7;
}

.floating-box {
    background: linear-gradient(145deg, #D2691E, #CD853F);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    text-align: center;
    max-width: 500px;
    margin: 20px;
    animation: floatBox 3s ease-in-out infinite;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.floating-box:hover {
    transform: scale(1.05);
}

@keyframes floatBox {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.opening-text {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    color: #FFD700;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.subtitle {
    font-size: 1.3rem;
    color: #FFF8DC;
    line-height: 1.8;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    margin-bottom: 10px;
    font-weight: 500;
}

.subtitle-small {
    font-size: 1.1rem;
    color: #FFF8DC;
    line-height: 1.6;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    opacity: 0.9;
}

/* Scene 1: High School Memories */
#scene1 {
    background: linear-gradient(to bottom, #E6E6FA 0%, #F0E68C 100%);
}

.school-background {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 100%;
    height: 60%;
}

.classroom {
    width: 200px;
    height: 250px;
    background: #DEB887;
    border-radius: 10px;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.door {
    width: 80%;
    height: 80%;
    background: #8B4513;
    margin: 10% auto;
    border-radius: 5px;
    position: relative;
    overflow: hidden;
}

.silhouette {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 60px;
    background: #2F4F4F;
    border-radius: 20px 20px 0 0;
}

.class-label {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: #FFD700;
    padding: 5px 15px;
    border-radius: 15px;
    font-weight: 600;
    color: #8B4513;
}

.corridor {
    width: 100px;
    height: 20px;
    background: #D2B48C;
    border-radius: 10px;
}

.falling-leaves {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.leaf {
    position: absolute;
    width: 20px;
    height: 20px;
    background: #228B22;
    border-radius: 0 100% 0 100%;
    animation: leafFall 4s infinite ease-in-out;
    cursor: pointer;
    pointer-events: all;
}

.leaf1 { top: -50px; left: 20%; animation-delay: 0s; }
.leaf2 { top: -50px; left: 40%; animation-delay: 1s; }
.leaf3 { top: -50px; left: 60%; animation-delay: 2s; }
.leaf4 { top: -50px; left: 80%; animation-delay: 3s; }

@keyframes leafFall {
    0% { transform: translateY(0) rotate(0deg); opacity: 1; }
    100% { transform: translateY(100vh) rotate(360deg); opacity: 0; }
}

.memory-text {
    position: absolute;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    max-width: 600px;
    padding: 20px;
}

.memory-text .main-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1.4rem;
    color: #8B4513;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    font-weight: 600;
    margin-bottom: 15px;
    line-height: 1.6;
}

.memory-text .sub-text {
    font-family: 'Dancing Script', cursive;
    font-size: 1.6rem;
    color: #8B4513;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    line-height: 1.5;
}

/* Buttons */
.next-btn, .restart-btn {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    padding: 15px 30px;
    background: linear-gradient(145deg, #FFD700, #FFA500);
    border: none;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    color: #8B4513;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.next-btn:hover, .restart-btn:hover {
    transform: translateX(-50%) scale(1.05);
    box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
}

/* Scene 2: Matcha & Sky */
#scene2 {
    background: linear-gradient(to bottom, #98FB98 0%, #90EE90 50%, #8FBC8F 100%);
}

.tea-garden {
    position: relative;
    width: 100%;
    height: 80%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.hills {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 200px;
    background: linear-gradient(to top, #228B22, #32CD32);
    border-radius: 50% 50% 0 0;
}

.tea-plants {
    position: absolute;
    bottom: 50px;
    display: flex;
    gap: 50px;
}

.plant {
    width: 60px;
    height: 80px;
    background: #006400;
    border-radius: 30px 30px 0 0;
    position: relative;
    animation: plantSway 3s ease-in-out infinite;
}

.plant1 { animation-delay: 0s; }
.plant2 { animation-delay: 1s; }
.plant3 { animation-delay: 2s; }

@keyframes plantSway {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(5deg); }
}

.ghibli-character {
    position: relative;
    z-index: 5;
}

.character-body {
    width: 80px;
    height: 100px;
    background: #DEB887;
    border-radius: 40px 40px 20px 20px;
    position: relative;
    animation: characterBreathe 2s ease-in-out infinite;
}

.character-body::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 50px;
    background: #F5DEB3;
    border-radius: 50%;
}

@keyframes characterBreathe {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.matcha-cup {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 30px;
    background: #8FBC8F;
    border-radius: 0 0 20px 20px;
    border: 3px solid #556B2F;
}

.matcha-cup::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 35px;
    height: 10px;
    background: #9ACD32;
    border-radius: 50%;
}

.matcha-text {
    position: absolute;
    bottom: 120px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    max-width: 600px;
    padding: 20px;
}

.matcha-text .main-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1.3rem;
    color: #2F4F4F;
    margin-bottom: 15px;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    font-weight: 600;
    line-height: 1.6;
}

.matcha-text .sub-text {
    font-family: 'Dancing Script', cursive;
    font-size: 1.4rem;
    color: #2F4F4F;
    margin-bottom: 10px;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    line-height: 1.5;
}

/* Scene 3: Declaration */
#scene3 {
    background: linear-gradient(to bottom, #191970 0%, #483D8B 50%, #6A5ACD 100%);
}

.night-sky {
    position: relative;
    width: 100%;
    height: 100%;
}

.stars {
    position: absolute;
    width: 100%;
    height: 100%;
}

.star {
    position: absolute;
    width: 6px;
    height: 6px;
    background: #FFD700;
    border-radius: 50%;
    animation: starTwinkle 2s ease-in-out infinite;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.star:hover {
    transform: scale(1.5);
}

.star1 { top: 20%; left: 20%; animation-delay: 0s; }
.star2 { top: 15%; left: 70%; animation-delay: 0.5s; }
.star3 { top: 30%; left: 50%; animation-delay: 1s; }
.star4 { top: 25%; left: 80%; animation-delay: 1.5s; }
.star5 { top: 35%; left: 30%; animation-delay: 2s; }

@keyframes starTwinkle {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}

.borobudur {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 300px;
    height: 150px;
    background: linear-gradient(to top, #2F4F4F, #696969);
    clip-path: polygon(0% 100%, 10% 80%, 20% 85%, 30% 70%, 40% 75%, 50% 60%, 60% 75%, 70% 70%, 80% 85%, 90% 80%, 100% 100%);
    opacity: 0.8;
}

.flying-letter {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.98);
    padding: 35px;
    border-radius: 15px;
    max-width: 550px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
    animation: letterFloat 4s ease-in-out infinite;
    z-index: 15;
}

@keyframes letterFloat {
    0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
    25% { transform: translate(-48%, -52%) rotate(1deg); }
    75% { transform: translate(-52%, -48%) rotate(-1deg); }
}

.letter-content .letter-title {
    font-family: 'Dancing Script', cursive;
    font-size: 1.8rem;
    color: #8B4513;
    margin-bottom: 20px;
    line-height: 1.4;
    text-align: center;
    font-weight: 600;
}

.letter-content .main-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1.1rem;
    color: #2F4F4F;
    margin-bottom: 15px;
    line-height: 1.7;
    text-align: left;
    font-weight: 500;
}

.letter-content .sub-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    color: #696969;
    margin-bottom: 10px;
    line-height: 1.6;
    text-align: left;
    font-style: italic;
}

.golden-text {
    position: absolute;
    bottom: 200px;
    left: 50%;
    transform: translateX(-50%);
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    color: #FFD700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    animation: goldenGlow 3s ease-in-out infinite;
    z-index: 10;
    background: rgba(0, 0, 0, 0.3);
    padding: 10px 20px;
    border-radius: 10px;
}

@keyframes goldenGlow {
    0%, 100% { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8); }
    50% { text-shadow: 0 0 20px #FFD700, 2px 2px 4px rgba(0, 0, 0, 0.8); }
}

/* Scene 5: Final Message */
#scene5 {
    background: linear-gradient(to bottom, #F0E68C 0%, #FFE4B5 50%, #FFF8DC 100%);
}

.final-background {
    position: relative;
    width: 100%;
    height: 100%;
}

.gentle-light {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, rgba(255, 215, 0, 0.1) 50%, transparent 100%);
    border-radius: 50%;
    animation: gentleGlow 4s ease-in-out infinite;
}

@keyframes gentleGlow {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.7; }
    50% { transform: translate(-50%, -50%) scale(1.1); opacity: 1; }
}

.floating-hearts {
    position: absolute;
    width: 100%;
    height: 100%;
}

.heart {
    position: absolute;
    font-size: 2rem;
    animation: heartFloat 6s ease-in-out infinite;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.heart:hover {
    transform: scale(1.3);
}

.heart1 {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.heart2 {
    top: 30%;
    right: 25%;
    animation-delay: 2s;
}

.heart3 {
    bottom: 25%;
    left: 60%;
    animation-delay: 4s;
}

@keyframes heartFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.8; }
    33% { transform: translateY(-15px) rotate(5deg); opacity: 1; }
    66% { transform: translateY(10px) rotate(-3deg); opacity: 0.9; }
}

.final-message {
    position: relative;
    z-index: 15;
    background: rgba(255, 255, 255, 0.98);
    padding: 35px;
    border-radius: 20px;
    max-width: 650px;
    margin: 20px;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
    text-align: center;
}

.final-title {
    font-family: 'Dancing Script', cursive;
    font-size: 2.2rem;
    color: #8B4513;
    margin-bottom: 25px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.final-message .main-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1.3rem;
    color: #2F4F4F;
    margin-bottom: 15px;
    line-height: 1.7;
    font-weight: 500;
}

.final-message .sub-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1.1rem;
    color: #696969;
    margin-bottom: 12px;
    line-height: 1.6;
    font-style: italic;
}

.final-message .hope-text {
    font-family: 'Dancing Script', cursive;
    font-size: 1.6rem;
    color: #D2691E;
    margin: 20px 0;
    line-height: 1.5;
    font-weight: 600;
}

.final-message .gratitude-text {
    font-family: 'Dancing Script', cursive;
    font-size: 1.5rem;
    color: #8B4513;
    margin-top: 25px;
    line-height: 1.4;
    font-weight: 500;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .loading-title {
        font-size: 2rem;
    }
    
    .loading-text {
        font-size: 1.1rem;
    }
    
    .loading-container {
        max-width: 90%;
        padding: 15px;
    }

    .opening-text {
        font-size: 2.2rem;
        line-height: 1.3;
    }

    .subtitle {
        font-size: 1.1rem;
        line-height: 1.6;
    }

    .subtitle-small {
        font-size: 1rem;
        line-height: 1.5;
    }

    .floating-box {
        padding: 25px;
        margin: 15px;
        max-width: 90%;
    }

    .classroom {
        width: 120px;
        height: 150px;
    }

    .memory-text {
        max-width: 90%;
        padding: 20px;
    }

    .memory-text .main-text {
        font-size: 1.2rem;
        line-height: 1.6;
    }

    .memory-text .sub-text {
        font-size: 1.4rem;
        line-height: 1.5;
    }

    .matcha-text {
        max-width: 90%;
        padding: 20px;
    }

    .matcha-text .main-text {
        font-size: 1.1rem;
        line-height: 1.6;
    }

    .matcha-text .sub-text {
        font-size: 1.2rem;
        line-height: 1.5;
    }

    .flying-letter {
        max-width: 90%;
        padding: 25px;
        margin: 10px;
    }

    .letter-content .letter-title {
        font-size: 1.6rem;
        line-height: 1.3;
    }

    .letter-content .main-text {
        font-size: 1rem;
        line-height: 1.7;
    }

    .letter-content .sub-text {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .golden-text {
        font-size: 2rem;
        line-height: 1.2;
    }

    .closing-text {
        max-width: 90%;
        padding: 25px;
    }

    .closing-text .main-text {
        font-size: 1.1rem;
        line-height: 1.7;
    }

    .closing-text .sub-text {
        font-size: 1rem;
        line-height: 1.6;
    }

    .closing-text .hope-text {
        font-size: 1.3rem;
        line-height: 1.4;
    }

    .final-message {
        max-width: 90%;
        padding: 25px;
    }

    .final-title {
        font-size: 1.8rem;
        line-height: 1.3;
    }

    .final-message .main-text {
        font-size: 1.1rem;
        line-height: 1.7;
    }

    .final-message .sub-text {
        font-size: 1rem;
        line-height: 1.6;
    }

    .final-message .hope-text {
        font-size: 1.4rem;
        line-height: 1.4;
    }

    .final-message .gratitude-text {
        font-size: 1.3rem;
        line-height: 1.3;
    }

    .audio-controls {
        top: 10px;
        right: 10px;
    }

    .music-btn, .day-night-btn {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }

    .borobudur {
        width: 250px;
        height: 120px;
    }
}

/* New Scenes Styling */

/* Scene 6: Journey Begins */
#scene6 {
    background: linear-gradient(to bottom, #87CEEB 0%, #DDA0DD 50%, #F0E68C 100%);
}

.journey-background {
    position: relative;
    width: 100%;
    height: 100%;
}

.path-forward {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background: linear-gradient(to right, #D2B48C 0%, #DEB887 50%, #F4A460 100%);
    clip-path: polygon(0% 100%, 100% 100%, 90% 0%, 10% 0%);
}

.mountains-distance {
    position: absolute;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    width: 400px;
    height: 100px;
    background: linear-gradient(to top, #696969, #A9A9A9);
    clip-path: polygon(0% 100%, 20% 60%, 40% 80%, 60% 40%, 80% 70%, 100% 100%);
    opacity: 0.6;
}

.journey-text, .growth-text, .character-text, .challenge-text, .purpose-text, .worthy-text, .promise-text {
    position: relative;
    z-index: 10;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    max-width: 600px;
    margin: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    text-align: center;
}

.chapter-title {
    font-family: 'Dancing Script', cursive;
    font-size: 2rem;
    color: #8B4513;
    margin-bottom: 20px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    border-bottom: 2px solid #FFD700;
    padding-bottom: 10px;
}

/* Scene 7: Growth */
#scene7 {
    background: linear-gradient(to bottom, #98FB98 0%, #90EE90 50%, #F0E68C 100%);
}

.growth-background {
    position: relative;
    width: 100%;
    height: 100%;
}

.books-stack {
    position: absolute;
    bottom: 20%;
    left: 20%;
    width: 60px;
    height: 80px;
    background: linear-gradient(to top, #8B4513, #D2691E, #CD853F);
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.books-stack::before {
    content: '';
    position: absolute;
    top: -15px;
    left: 5px;
    width: 50px;
    height: 12px;
    background: #A0522D;
    border-radius: 3px;
}

.books-stack::after {
    content: '';
    position: absolute;
    top: -30px;
    left: 10px;
    width: 40px;
    height: 12px;
    background: #D2691E;
    border-radius: 3px;
}

.growing-tree {
    position: absolute;
    bottom: 10%;
    right: 25%;
    width: 80px;
    height: 120px;
    background: linear-gradient(to top, #8B4513 0%, #8B4513 60%, #228B22 60%, #32CD32 100%);
    border-radius: 40px 40px 10px 10px;
}

.growing-tree::before {
    content: '';
    position: absolute;
    top: 10px;
    left: -20px;
    width: 40px;
    height: 40px;
    background: #32CD32;
    border-radius: 50%;
}

.growing-tree::after {
    content: '';
    position: absolute;
    top: 15px;
    right: -15px;
    width: 35px;
    height: 35px;
    background: #228B22;
    border-radius: 50%;
}

.floating-knowledge {
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 2rem;
    animation: floatKnowledge 4s ease-in-out infinite;
}

.floating-knowledge::before {
    content: '📚✨💡';
    opacity: 0.7;
}

/* Scene 8: Character Building */
#scene8 {
    background: linear-gradient(to bottom, #E6E6FA 0%, #DDA0DD 50%, #D8BFD8 100%);
}

.character-background {
    position: relative;
    width: 100%;
    height: 100%;
}

.mirror-reflection {
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    height: 200px;
    background: linear-gradient(145deg, #C0C0C0, #E5E5E5);
    border-radius: 10px;
    border: 5px solid #B8860B;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.mirror-reflection::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    width: 110px;
    height: 160px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.3));
    border-radius: 5px;
}

.inner-light {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.6) 0%, transparent 70%);
    border-radius: 50%;
    animation: innerGlow 3s ease-in-out infinite;
}

/* Scene 9: Challenges */
#scene9 {
    background: linear-gradient(to bottom, #2F4F4F 0%, #696969 50%, #778899 100%);
}

.challenge-background {
    position: relative;
    width: 100%;
    height: 100%;
}

.storm-clouds {
    position: absolute;
    top: 10%;
    left: 0;
    width: 100%;
    height: 60px;
    background: linear-gradient(to right, #2F4F4F, #696969, #2F4F4F);
    border-radius: 30px;
    opacity: 0.8;
}

.storm-clouds::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 20%;
    width: 80px;
    height: 40px;
    background: #2F4F4F;
    border-radius: 50%;
}

.storm-clouds::after {
    content: '';
    position: absolute;
    top: 25px;
    right: 25%;
    width: 60px;
    height: 30px;
    background: #696969;
    border-radius: 50%;
}

.lightning {
    position: absolute;
    top: 25%;
    left: 60%;
    width: 4px;
    height: 100px;
    background: linear-gradient(to bottom, #FFD700, #FFA500, transparent);
    transform: rotate(15deg);
}

.standing-figure {
    position: absolute;
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 80px;
    background: linear-gradient(to top, #8B4513, #D2691E);
    border-radius: 20px 20px 5px 5px;
}

/* Scene 10: Purpose */
#scene10 {
    background: linear-gradient(to bottom, #191970 0%, #4B0082 50%, #8A2BE2 100%);
}

.purpose-background {
    position: relative;
    width: 100%;
    height: 100%;
}

.compass {
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, #B8860B 0%, #DAA520 50%, #B8860B 100%);
    border-radius: 50%;
    border: 5px solid #8B4513;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

.compass::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 2px;
    background: #FF0000;
    border-radius: 1px;
}

.compass::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(90deg);
    width: 60px;
    height: 2px;
    background: #000000;
    border-radius: 1px;
}

.north-star {
    position: absolute;
    top: 15%;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 20px;
    background: #FFD700;
    clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
}

/* Scene 11: Worthy */
#scene11 {
    background: linear-gradient(to bottom, #FFE4B5 0%, #FFEAA7 50%, #FFD700 100%);
}

.worthy-background {
    position: relative;
    width: 100%;
    height: 100%;
}

.golden-aura {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.4) 0%, rgba(255, 215, 0, 0.1) 50%, transparent 100%);
    border-radius: 50%;
}

.achievement-symbols {
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 3rem;
    opacity: 0.7;
}

.achievement-symbols::before {
    content: '🏆⭐🎯';
}

/* Scene 12: Promise */
#scene12 {
    background: linear-gradient(to bottom, #8B0000 0%, #DC143C 50%, #FF6347 100%);
}

.promise-background {
    position: relative;
    width: 100%;
    height: 100%;
}

.eternal-flame {
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 100px;
    background: linear-gradient(to top, #FF4500 0%, #FF6347 30%, #FFD700 60%, #FFA500 100%);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
}

.guardian-silhouette {
    position: absolute;
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 120px;
    background: linear-gradient(to top, #2F4F4F, #696969);
    border-radius: 30px 30px 10px 10px;
    opacity: 0.8;
}

/* Scene 13: Dedication */
#scene13 {
    background: linear-gradient(to bottom, #000080 0%, #191970 50%, #4B0082 100%);
}

.dedication-background {
    position: relative;
    width: 100%;
    height: 100%;
}

.infinite-stars {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #FFD700, transparent),
        radial-gradient(2px 2px at 40px 70px, #FFA500, transparent),
        radial-gradient(1px 1px at 90px 40px, #FFD700, transparent),
        radial-gradient(1px 1px at 130px 80px, #FFA500, transparent),
        radial-gradient(2px 2px at 160px 30px, #FFD700, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
}

.golden-inscription {
    position: absolute;
    bottom: 30%;
    left: 50%;
    transform: translateX(-50%);
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    color: #FFD700;
    text-shadow: 0 0 20px #FFD700;
    text-align: center;
}

.golden-inscription::before {
    content: 'Aurum Splendet in Aeternum';
}

/* New Animations */
@keyframes pathExtend {
    0% { width: 0%; }
    100% { width: 100%; }
}

@keyframes stackGrow {
    0% { transform: scale(0.5); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes treeGrow {
    0% { height: 0px; }
    100% { height: 120px; }
}

@keyframes floatKnowledge {
    0%, 100% { transform: translateX(-50%) translateY(0px); }
    50% { transform: translateX(-50%) translateY(-20px); }
}

@keyframes mirrorShine {
    0% { box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); }
    50% { box-shadow: 0 10px 30px rgba(255, 215, 0, 0.5); }
    100% { box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); }
}

@keyframes innerGlow {
    0%, 100% { opacity: 0.6; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes stormMove {
    0%, 100% { transform: translateX(0px); }
    50% { transform: translateX(20px); }
}

@keyframes lightningFlash {
    0%, 90%, 100% { opacity: 0; }
    5%, 10% { opacity: 1; }
}

@keyframes compassSpin {
    0% { transform: translateX(-50%) rotate(0deg); }
    100% { transform: translateX(-50%) rotate(360deg); }
}

@keyframes auraGlow {
    0%, 100% { opacity: 0.4; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes flameFlicker {
    0%, 100% { transform: translateX(-50%) scale(1); }
    25% { transform: translateX(-50%) scale(1.05) rotate(2deg); }
    75% { transform: translateX(-50%) scale(0.95) rotate(-2deg); }
}

@keyframes starsRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes inscriptionGlow {
    0%, 100% { text-shadow: 0 0 20px #FFD700; }
    50% { text-shadow: 0 0 40px #FFD700, 0 0 60px #FFA500; }
}

/* Scene 4: Closing */
#scene4 {
    background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 50%, #F0E68C 100%);
}

.jogja-path {
    position: relative;
    width: 100%;
    height: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.path {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 100px;
    background: linear-gradient(to right, #D2B48C 0%, #DEB887 50%, #D2B48C 100%);
    border-radius: 50px 50px 0 0;
}

.path::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 20px;
    background: #CD853F;
    border-radius: 10px;
}

.arum-character {
    position: relative;
    z-index: 5;
    animation: walkAnimation 4s ease-in-out infinite;
}

@keyframes walkAnimation {
    0%, 100% { transform: translateX(0px); }
    50% { transform: translateX(30px); }
}

.character-walking {
    width: 60px;
    height: 120px;
    background: linear-gradient(to bottom, #F5DEB3 0%, #F5DEB3 40%, #FF69B4 40%, #FF69B4 70%, #8B4513 70%);
    border-radius: 30px 30px 10px 10px;
    position: relative;
    animation: characterWalk 1s ease-in-out infinite;
}

.character-walking::before {
    content: '';
    position: absolute;
    top: 5px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background: #F5DEB3;
    border-radius: 50%;
}

.character-walking::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 20px;
    background: #8B4513;
    border-radius: 15px 15px 0 0;
}

@keyframes characterWalk {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.02) rotate(1deg); }
}

.birds {
    position: absolute;
    width: 100%;
    height: 100%;
}

.bird {
    position: absolute;
    width: 20px;
    height: 15px;
    background: #8B4513;
    border-radius: 50% 50% 0 50%;
    animation: birdFly 6s ease-in-out infinite;
    cursor: pointer;
}

.bird::before {
    content: '';
    position: absolute;
    top: 2px;
    right: -10px;
    width: 15px;
    height: 10px;
    background: #8B4513;
    border-radius: 50% 0 50% 50%;
}

.bird1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.bird2 {
    top: 25%;
    left: 20%;
    animation-delay: 2s;
}

.bird3 {
    top: 30%;
    left: 15%;
    animation-delay: 4s;
}

@keyframes birdFly {
    0% { transform: translateX(0px) translateY(0px); }
    25% { transform: translateX(100px) translateY(-20px); }
    50% { transform: translateX(200px) translateY(10px); }
    75% { transform: translateX(300px) translateY(-15px); }
    100% { transform: translateX(400px) translateY(5px); }
}

.closing-text {
    position: absolute;
    bottom: 150px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    max-width: 600px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    z-index: 10;
}

.closing-text .main-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1.2rem;
    color: #8B4513;
    margin-bottom: 15px;
    line-height: 1.7;
    font-weight: 500;
}

.closing-text .sub-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1.1rem;
    color: #696969;
    margin-bottom: 12px;
    line-height: 1.6;
    font-style: italic;
}

.closing-text .hope-text {
    font-family: 'Dancing Script', cursive;
    font-size: 1.5rem;
    color: #D2691E;
    margin-top: 20px;
    line-height: 1.5;
    font-weight: 600;
    text-align: center;
}

/* Star Particle Effect */
.star-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #FFD700;
    border-radius: 50%;
    pointer-events: none;
    animation: starParticle 2s ease-out forwards;
}

@keyframes starParticle {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(0) translateY(-50px);
    }
}

@keyframes heartParticle {
    0% {
        opacity: 1;
        transform: scale(1) translateY(0px);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.2) translateY(-30px);
    }
    100% {
        opacity: 0;
        transform: scale(0.5) translateY(-60px);
    }
}

/* Additional Mobile Responsive */
@media (max-width: 480px) {
    .loading-title {
        font-size: 1.8rem;
    }
    
    .loading-text {
        font-size: 1rem;
    }
    
    .spinner-circle {
        width: 10px;
        height: 10px;
    }
    
    .start-button {
        font-size: 1rem;
        padding: 12px 25px;
    }

    body {
        font-size: 16px; /* Base font size for better readability */
    }

    .floating-box {
        padding: 20px;
        margin: 10px;
        max-width: 95%;
    }

    .opening-text {
        font-size: 2rem;
        line-height: 1.2;
        margin-bottom: 15px;
    }

    .subtitle {
        font-size: 1rem;
        line-height: 1.5;
        margin-bottom: 8px;
    }

    .subtitle-small {
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .memory-text {
        padding: 15px;
        max-width: 95%;
    }

    .memory-text .main-text {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 12px;
    }

    .memory-text .sub-text {
        font-size: 1.3rem;
        line-height: 1.4;
    }

    .matcha-text {
        padding: 15px;
        max-width: 95%;
    }

    .matcha-text .main-text {
        font-size: 1rem;
        line-height: 1.6;
        margin-bottom: 12px;
    }

    .matcha-text .sub-text {
        font-size: 1.1rem;
        line-height: 1.4;
    }

    .flying-letter {
        max-width: 95%;
        padding: 20px;
        margin: 5px;
    }

    .letter-content .letter-title {
        font-size: 1.4rem;
        line-height: 1.2;
        margin-bottom: 15px;
    }

    .letter-content .main-text {
        font-size: 0.95rem;
        line-height: 1.7;
        margin-bottom: 12px;
    }

    .letter-content .sub-text {
        font-size: 0.9rem;
        line-height: 1.6;
        margin-bottom: 8px;
    }

    .golden-text {
        font-size: 1.8rem;
        line-height: 1.1;
        padding: 0 10px;
    }

    .closing-text {
        padding: 20px;
        max-width: 95%;
    }

    .closing-text .main-text {
        font-size: 1rem;
        line-height: 1.7;
        margin-bottom: 12px;
    }

    .closing-text .sub-text {
        font-size: 0.95rem;
        line-height: 1.6;
        margin-bottom: 10px;
    }

    .closing-text .hope-text {
        font-size: 1.2rem;
        line-height: 1.3;
        margin-top: 15px;
    }

    .character-walking {
        width: 50px;
        height: 100px;
    }

    .tea-plants {
        gap: 30px;
    }

    .plant {
        width: 40px;
        height: 60px;
    }

    .next-btn, .restart-btn {
        padding: 12px 25px;
        font-size: 1rem;
        margin-bottom: 20px;
    }

    .audio-controls {
        top: 5px;
        right: 5px;
    }

    .music-btn, .day-night-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .final-message {
        padding: 20px;
        max-width: 95%;
    }

    .final-title {
        font-size: 1.6rem;
        line-height: 1.2;
        margin-bottom: 20px;
    }

    .final-message .main-text {
        font-size: 1rem;
        line-height: 1.7;
        margin-bottom: 12px;
    }

    .final-message .sub-text {
        font-size: 0.95rem;
        line-height: 1.6;
        margin-bottom: 10px;
    }

    .final-message .hope-text {
        font-size: 1.2rem;
        line-height: 1.3;
        margin: 15px 0;
    }

    .final-message .gratitude-text {
        font-size: 1.1rem;
        line-height: 1.2;
        margin-top: 20px;
    }

    .heart {
        font-size: 1.5rem;
    }

    /* New scenes mobile responsiveness */
    .chapter-title {
        font-size: 1.6rem;
        margin-bottom: 15px;
    }

    .journey-text, .growth-text, .character-text, .challenge-text,
    .purpose-text, .worthy-text, .promise-text {
        padding: 20px;
        max-width: 95%;
    }

    .mountains-distance {
        width: 300px;
        height: 80px;
    }

    .books-stack {
        width: 50px;
        height: 60px;
    }

    .growing-tree {
        width: 60px;
        height: 90px;
    }

    .mirror-reflection {
        width: 120px;
        height: 160px;
    }

    .compass {
        width: 80px;
        height: 80px;
    }

    .golden-aura {
        width: 150px;
        height: 150px;
    }

    .achievement-symbols {
        font-size: 2rem;
    }

    .eternal-flame {
        width: 40px;
        height: 70px;
    }

    .guardian-silhouette {
        width: 40px;
        height: 80px;
    }

    .golden-inscription {
        font-size: 1.8rem;
        padding: 0 10px;
    }
}

/* Additional Animations */
@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes doorOpen {
    0% { transform: scaleX(1); }
    50% { transform: scaleX(0.8); }
    100% { transform: scaleX(1); }
}

@keyframes characterAppear {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes letterFlyIn {
    0% {
        opacity: 0;
        transform: translate(-50%, -100%) scale(0.5);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes goldenAppear {
    0% {
        opacity: 0;
        transform: translateX(-50%) scale(0.5);
    }
    100% {
        opacity: 1;
        transform: translateX(-50%) scale(1);
    }
}

@keyframes characterWalkIn {
    0% {
        opacity: 0;
        transform: translateX(-100px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes leafSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes plantDance {
    0%, 100% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(-10deg) scale(1.1); }
    75% { transform: rotate(10deg) scale(1.1); }
}

@keyframes birdFlutter {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2) rotate(10deg); }
}

/* Loading Animation */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-text {
    font-family: 'Dancing Script', cursive;
    font-size: 3rem;
    color: #8B4513;
    animation: loadingPulse 2s ease-in-out infinite;
}

@keyframes loadingPulse {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

/* Smooth transitions for all interactive elements */
.cloud, .leaf, .star, .plant, .bird, .floating-box {
    transition: all 0.3s ease;
}

/* Hover effects for better interactivity */
.leaf:hover {
    transform: scale(1.2) rotate(15deg);
}

.plant:hover {
    transform: scale(1.1);
}

.bird:hover {
    transform: scale(1.2);
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 215, 0, 0.8);
}
