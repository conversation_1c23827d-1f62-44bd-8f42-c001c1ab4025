/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    overflow: hidden;
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
    transition: all 0.5s ease;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

body.night-mode {
    background: linear-gradient(135deg, #2d3436 0%, #636e72 50%, #74b9ff 100%);
}

/* Audio Controls */
.audio-controls {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    gap: 10px;
}

.music-btn, .day-night-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.music-btn:hover, .day-night-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* Particles */
#particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #ffd700;
    border-radius: 50%;
    animation: float 6s infinite ease-in-out;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 0.7; }
}

/* Scene Base Styles */
.scene {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transform: translateX(100%);
    transition: all 1s ease-in-out;
    padding: 20px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.scene.active {
    opacity: 1;
    transform: translateX(0);
}

/* Scene 0: Opening */
#scene0 {
    background: linear-gradient(to bottom, #87CEEB 0%, #FFE4B5 50%, #98FB98 100%);
}

.sky-background {
    position: absolute;
    width: 100%;
    height: 100%;
}

.cloud {
    position: absolute;
    background: white;
    border-radius: 50px;
    opacity: 0.8;
    animation: cloudFloat 20s infinite ease-in-out;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.cloud:hover {
    transform: scale(1.1);
}

.cloud1 {
    width: 100px;
    height: 40px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.cloud2 {
    width: 150px;
    height: 60px;
    top: 15%;
    right: 20%;
    animation-delay: -5s;
}

.cloud3 {
    width: 80px;
    height: 30px;
    top: 30%;
    left: 60%;
    animation-delay: -10s;
}

@keyframes cloudFloat {
    0%, 100% { transform: translateX(0px); }
    50% { transform: translateX(30px); }
}

.mountain.merapi {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 200px solid transparent;
    border-right: 200px solid transparent;
    border-bottom: 150px solid #8B4513;
    opacity: 0.7;
}

.floating-box {
    background: linear-gradient(145deg, #D2691E, #CD853F);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    text-align: center;
    max-width: 500px;
    margin: 20px;
    animation: floatBox 3s ease-in-out infinite;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.floating-box:hover {
    transform: scale(1.05);
}

@keyframes floatBox {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.opening-text {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    color: #FFD700;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.subtitle {
    font-size: 1.3rem;
    color: #FFF8DC;
    line-height: 1.8;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    margin-bottom: 10px;
    font-weight: 500;
}

.subtitle-small {
    font-size: 1.1rem;
    color: #FFF8DC;
    line-height: 1.6;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    opacity: 0.9;
}

/* Scene 1: High School Memories */
#scene1 {
    background: linear-gradient(to bottom, #E6E6FA 0%, #F0E68C 100%);
}

.school-background {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 100%;
    height: 60%;
}

.classroom {
    width: 200px;
    height: 250px;
    background: #DEB887;
    border-radius: 10px;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.door {
    width: 80%;
    height: 80%;
    background: #8B4513;
    margin: 10% auto;
    border-radius: 5px;
    position: relative;
    overflow: hidden;
}

.silhouette {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 60px;
    background: #2F4F4F;
    border-radius: 20px 20px 0 0;
}

.class-label {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: #FFD700;
    padding: 5px 15px;
    border-radius: 15px;
    font-weight: 600;
    color: #8B4513;
}

.corridor {
    width: 100px;
    height: 20px;
    background: #D2B48C;
    border-radius: 10px;
}

.falling-leaves {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.leaf {
    position: absolute;
    width: 20px;
    height: 20px;
    background: #228B22;
    border-radius: 0 100% 0 100%;
    animation: leafFall 4s infinite ease-in-out;
    cursor: pointer;
    pointer-events: all;
}

.leaf1 { top: -50px; left: 20%; animation-delay: 0s; }
.leaf2 { top: -50px; left: 40%; animation-delay: 1s; }
.leaf3 { top: -50px; left: 60%; animation-delay: 2s; }
.leaf4 { top: -50px; left: 80%; animation-delay: 3s; }

@keyframes leafFall {
    0% { transform: translateY(0) rotate(0deg); opacity: 1; }
    100% { transform: translateY(100vh) rotate(360deg); opacity: 0; }
}

.memory-text {
    position: absolute;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    max-width: 600px;
    padding: 20px;
}

.memory-text .main-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1.4rem;
    color: #8B4513;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    font-weight: 600;
    margin-bottom: 15px;
    line-height: 1.6;
}

.memory-text .sub-text {
    font-family: 'Dancing Script', cursive;
    font-size: 1.6rem;
    color: #8B4513;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    line-height: 1.5;
}

/* Buttons */
.next-btn, .restart-btn {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    padding: 15px 30px;
    background: linear-gradient(145deg, #FFD700, #FFA500);
    border: none;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    color: #8B4513;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.next-btn:hover, .restart-btn:hover {
    transform: translateX(-50%) scale(1.05);
    box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
}

/* Scene 2: Matcha & Sky */
#scene2 {
    background: linear-gradient(to bottom, #98FB98 0%, #90EE90 50%, #8FBC8F 100%);
}

.tea-garden {
    position: relative;
    width: 100%;
    height: 80%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.hills {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 200px;
    background: linear-gradient(to top, #228B22, #32CD32);
    border-radius: 50% 50% 0 0;
}

.tea-plants {
    position: absolute;
    bottom: 50px;
    display: flex;
    gap: 50px;
}

.plant {
    width: 60px;
    height: 80px;
    background: #006400;
    border-radius: 30px 30px 0 0;
    position: relative;
    animation: plantSway 3s ease-in-out infinite;
}

.plant1 { animation-delay: 0s; }
.plant2 { animation-delay: 1s; }
.plant3 { animation-delay: 2s; }

@keyframes plantSway {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(5deg); }
}

.ghibli-character {
    position: relative;
    z-index: 5;
}

.character-body {
    width: 80px;
    height: 100px;
    background: #DEB887;
    border-radius: 40px 40px 20px 20px;
    position: relative;
    animation: characterBreathe 2s ease-in-out infinite;
}

.character-body::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 50px;
    background: #F5DEB3;
    border-radius: 50%;
}

@keyframes characterBreathe {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.matcha-cup {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 30px;
    background: #8FBC8F;
    border-radius: 0 0 20px 20px;
    border: 3px solid #556B2F;
}

.matcha-cup::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 35px;
    height: 10px;
    background: #9ACD32;
    border-radius: 50%;
}

.matcha-text {
    position: absolute;
    bottom: 120px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    max-width: 600px;
    padding: 20px;
}

.matcha-text .main-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1.3rem;
    color: #2F4F4F;
    margin-bottom: 15px;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    font-weight: 600;
    line-height: 1.6;
}

.matcha-text .sub-text {
    font-family: 'Dancing Script', cursive;
    font-size: 1.4rem;
    color: #2F4F4F;
    margin-bottom: 10px;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    line-height: 1.5;
}

/* Scene 3: Declaration */
#scene3 {
    background: linear-gradient(to bottom, #191970 0%, #483D8B 50%, #6A5ACD 100%);
}

.night-sky {
    position: relative;
    width: 100%;
    height: 100%;
}

.stars {
    position: absolute;
    width: 100%;
    height: 100%;
}

.star {
    position: absolute;
    width: 6px;
    height: 6px;
    background: #FFD700;
    border-radius: 50%;
    animation: starTwinkle 2s ease-in-out infinite;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.star:hover {
    transform: scale(1.5);
}

.star1 { top: 20%; left: 20%; animation-delay: 0s; }
.star2 { top: 15%; left: 70%; animation-delay: 0.5s; }
.star3 { top: 30%; left: 50%; animation-delay: 1s; }
.star4 { top: 25%; left: 80%; animation-delay: 1.5s; }
.star5 { top: 35%; left: 30%; animation-delay: 2s; }

@keyframes starTwinkle {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}

.borobudur {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 300px;
    height: 150px;
    background: linear-gradient(to top, #2F4F4F, #696969);
    clip-path: polygon(0% 100%, 10% 80%, 20% 85%, 30% 70%, 40% 75%, 50% 60%, 60% 75%, 70% 70%, 80% 85%, 90% 80%, 100% 100%);
    opacity: 0.8;
}

.flying-letter {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    max-width: 500px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    animation: letterFloat 4s ease-in-out infinite;
}

@keyframes letterFloat {
    0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
    25% { transform: translate(-48%, -52%) rotate(1deg); }
    75% { transform: translate(-52%, -48%) rotate(-1deg); }
}

.letter-content .letter-title {
    font-family: 'Dancing Script', cursive;
    font-size: 1.8rem;
    color: #8B4513;
    margin-bottom: 20px;
    line-height: 1.4;
    text-align: center;
    font-weight: 600;
}

.letter-content .main-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1.1rem;
    color: #2F4F4F;
    margin-bottom: 15px;
    line-height: 1.7;
    text-align: left;
    font-weight: 500;
}

.letter-content .sub-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    color: #696969;
    margin-bottom: 10px;
    line-height: 1.6;
    text-align: left;
    font-style: italic;
}

.golden-text {
    position: absolute;
    bottom: 150px;
    left: 50%;
    transform: translateX(-50%);
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    color: #FFD700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    animation: goldenGlow 3s ease-in-out infinite;
}

@keyframes goldenGlow {
    0%, 100% { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8); }
    50% { text-shadow: 0 0 20px #FFD700, 2px 2px 4px rgba(0, 0, 0, 0.8); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .opening-text {
        font-size: 2.2rem;
        line-height: 1.3;
    }

    .subtitle {
        font-size: 1.1rem;
        line-height: 1.6;
    }

    .subtitle-small {
        font-size: 1rem;
        line-height: 1.5;
    }

    .floating-box {
        padding: 25px;
        margin: 15px;
        max-width: 90%;
    }

    .classroom {
        width: 120px;
        height: 150px;
    }

    .memory-text {
        max-width: 90%;
        padding: 20px;
    }

    .memory-text .main-text {
        font-size: 1.2rem;
        line-height: 1.6;
    }

    .memory-text .sub-text {
        font-size: 1.4rem;
        line-height: 1.5;
    }

    .matcha-text {
        max-width: 90%;
        padding: 20px;
    }

    .matcha-text .main-text {
        font-size: 1.1rem;
        line-height: 1.6;
    }

    .matcha-text .sub-text {
        font-size: 1.2rem;
        line-height: 1.5;
    }

    .flying-letter {
        max-width: 90%;
        padding: 25px;
        margin: 10px;
    }

    .letter-content .letter-title {
        font-size: 1.6rem;
        line-height: 1.3;
    }

    .letter-content .main-text {
        font-size: 1rem;
        line-height: 1.7;
    }

    .letter-content .sub-text {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .golden-text {
        font-size: 2rem;
        line-height: 1.2;
    }

    .closing-text {
        max-width: 90%;
        padding: 25px;
    }

    .closing-text .main-text {
        font-size: 1.1rem;
        line-height: 1.7;
    }

    .closing-text .sub-text {
        font-size: 1rem;
        line-height: 1.6;
    }

    .closing-text .hope-text {
        font-size: 1.3rem;
        line-height: 1.4;
    }

    .audio-controls {
        top: 10px;
        right: 10px;
    }

    .music-btn, .day-night-btn {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }

    .borobudur {
        width: 250px;
        height: 120px;
    }
}

/* Scene 4: Closing */
#scene4 {
    background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 50%, #F0E68C 100%);
}

.jogja-path {
    position: relative;
    width: 100%;
    height: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.path {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 100px;
    background: linear-gradient(to right, #D2B48C 0%, #DEB887 50%, #D2B48C 100%);
    border-radius: 50px 50px 0 0;
}

.path::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 20px;
    background: #CD853F;
    border-radius: 10px;
}

.arum-character {
    position: relative;
    z-index: 5;
    animation: walkAnimation 4s ease-in-out infinite;
}

@keyframes walkAnimation {
    0%, 100% { transform: translateX(0px); }
    50% { transform: translateX(30px); }
}

.character-walking {
    width: 60px;
    height: 120px;
    background: linear-gradient(to bottom, #F5DEB3 0%, #F5DEB3 40%, #FF69B4 40%, #FF69B4 70%, #8B4513 70%);
    border-radius: 30px 30px 10px 10px;
    position: relative;
    animation: characterWalk 1s ease-in-out infinite;
}

.character-walking::before {
    content: '';
    position: absolute;
    top: 5px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background: #F5DEB3;
    border-radius: 50%;
}

.character-walking::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 20px;
    background: #8B4513;
    border-radius: 15px 15px 0 0;
}

@keyframes characterWalk {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.02) rotate(1deg); }
}

.birds {
    position: absolute;
    width: 100%;
    height: 100%;
}

.bird {
    position: absolute;
    width: 20px;
    height: 15px;
    background: #8B4513;
    border-radius: 50% 50% 0 50%;
    animation: birdFly 6s ease-in-out infinite;
    cursor: pointer;
}

.bird::before {
    content: '';
    position: absolute;
    top: 2px;
    right: -10px;
    width: 15px;
    height: 10px;
    background: #8B4513;
    border-radius: 50% 0 50% 50%;
}

.bird1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.bird2 {
    top: 25%;
    left: 20%;
    animation-delay: 2s;
}

.bird3 {
    top: 30%;
    left: 15%;
    animation-delay: 4s;
}

@keyframes birdFly {
    0% { transform: translateX(0px) translateY(0px); }
    25% { transform: translateX(100px) translateY(-20px); }
    50% { transform: translateX(200px) translateY(10px); }
    75% { transform: translateX(300px) translateY(-15px); }
    100% { transform: translateX(400px) translateY(5px); }
}

.closing-text {
    position: absolute;
    bottom: 120px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    max-width: 600px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.closing-text .main-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1.2rem;
    color: #8B4513;
    margin-bottom: 15px;
    line-height: 1.7;
    font-weight: 500;
}

.closing-text .sub-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1.1rem;
    color: #696969;
    margin-bottom: 12px;
    line-height: 1.6;
    font-style: italic;
}

.closing-text .hope-text {
    font-family: 'Dancing Script', cursive;
    font-size: 1.5rem;
    color: #D2691E;
    margin-top: 20px;
    line-height: 1.5;
    font-weight: 600;
    text-align: center;
}

/* Star Particle Effect */
.star-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #FFD700;
    border-radius: 50%;
    pointer-events: none;
    animation: starParticle 2s ease-out forwards;
}

@keyframes starParticle {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(0) translateY(-50px);
    }
}

/* Additional Mobile Responsive */
@media (max-width: 480px) {
    body {
        font-size: 16px; /* Base font size for better readability */
    }

    .floating-box {
        padding: 20px;
        margin: 10px;
        max-width: 95%;
    }

    .opening-text {
        font-size: 2rem;
        line-height: 1.2;
        margin-bottom: 15px;
    }

    .subtitle {
        font-size: 1rem;
        line-height: 1.5;
        margin-bottom: 8px;
    }

    .subtitle-small {
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .memory-text {
        padding: 15px;
        max-width: 95%;
    }

    .memory-text .main-text {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 12px;
    }

    .memory-text .sub-text {
        font-size: 1.3rem;
        line-height: 1.4;
    }

    .matcha-text {
        padding: 15px;
        max-width: 95%;
    }

    .matcha-text .main-text {
        font-size: 1rem;
        line-height: 1.6;
        margin-bottom: 12px;
    }

    .matcha-text .sub-text {
        font-size: 1.1rem;
        line-height: 1.4;
    }

    .flying-letter {
        max-width: 95%;
        padding: 20px;
        margin: 5px;
    }

    .letter-content .letter-title {
        font-size: 1.4rem;
        line-height: 1.2;
        margin-bottom: 15px;
    }

    .letter-content .main-text {
        font-size: 0.95rem;
        line-height: 1.7;
        margin-bottom: 12px;
    }

    .letter-content .sub-text {
        font-size: 0.9rem;
        line-height: 1.6;
        margin-bottom: 8px;
    }

    .golden-text {
        font-size: 1.8rem;
        line-height: 1.1;
        padding: 0 10px;
    }

    .closing-text {
        padding: 20px;
        max-width: 95%;
    }

    .closing-text .main-text {
        font-size: 1rem;
        line-height: 1.7;
        margin-bottom: 12px;
    }

    .closing-text .sub-text {
        font-size: 0.95rem;
        line-height: 1.6;
        margin-bottom: 10px;
    }

    .closing-text .hope-text {
        font-size: 1.2rem;
        line-height: 1.3;
        margin-top: 15px;
    }

    .character-walking {
        width: 50px;
        height: 100px;
    }

    .tea-plants {
        gap: 30px;
    }

    .plant {
        width: 40px;
        height: 60px;
    }

    .next-btn, .restart-btn {
        padding: 12px 25px;
        font-size: 1rem;
        margin-bottom: 20px;
    }

    .audio-controls {
        top: 5px;
        right: 5px;
    }

    .music-btn, .day-night-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

/* Additional Animations */
@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes doorOpen {
    0% { transform: scaleX(1); }
    50% { transform: scaleX(0.8); }
    100% { transform: scaleX(1); }
}

@keyframes characterAppear {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes letterFlyIn {
    0% {
        opacity: 0;
        transform: translate(-50%, -100%) scale(0.5);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes goldenAppear {
    0% {
        opacity: 0;
        transform: translateX(-50%) scale(0.5);
    }
    100% {
        opacity: 1;
        transform: translateX(-50%) scale(1);
    }
}

@keyframes characterWalkIn {
    0% {
        opacity: 0;
        transform: translateX(-100px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes leafSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes plantDance {
    0%, 100% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(-10deg) scale(1.1); }
    75% { transform: rotate(10deg) scale(1.1); }
}

@keyframes birdFlutter {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2) rotate(10deg); }
}

/* Loading Animation */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-text {
    font-family: 'Dancing Script', cursive;
    font-size: 3rem;
    color: #8B4513;
    animation: loadingPulse 2s ease-in-out infinite;
}

@keyframes loadingPulse {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

/* Smooth transitions for all interactive elements */
.cloud, .leaf, .star, .plant, .bird, .floating-box {
    transition: all 0.3s ease;
}

/* Hover effects for better interactivity */
.leaf:hover {
    transform: scale(1.2) rotate(15deg);
}

.plant:hover {
    transform: scale(1.1);
}

.bird:hover {
    transform: scale(1.2);
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 215, 0, 0.8);
}
