// Global variables
let currentScene = 0;
let musicPlaying = false;
let isNightMode = false;
const totalScenes = 5;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    createParticles();
    setupEventListeners();
});

function initializeApp() {
    // Set initial scene
    showScene(0);
    
    // Initialize audio
    const music = document.getElementById('backgroundMusic');
    music.volume = 0.3;
    
    // Add click events to interactive elements
    addInteractiveElements();
}

function setupEventListeners() {
    // Music toggle
    document.getElementById('musicToggle').addEventListener('click', toggleMusic);
    
    // Day/Night toggle
    document.getElementById('dayNightToggle').addEventListener('click', toggleDayNight);
    
    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowRight' || e.key === ' ') {
            nextScene();
        } else if (e.key === 'ArrowLeft') {
            previousScene();
        } else if (e.key === 'r' || e.key === 'R') {
            restartStory();
        }
    });
    
    // Touch/swipe support for mobile
    let touchStartX = 0;
    let touchEndX = 0;
    
    document.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    });
    
    document.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    });
    
    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;
        
        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                nextScene(); // Swipe left - next scene
            } else {
                previousScene(); // Swipe right - previous scene
            }
        }
    }
}

function addInteractiveElements() {
    // Add click events to clouds
    document.querySelectorAll('.cloud').forEach(cloud => {
        cloud.addEventListener('click', function(e) {
            createCloudParticles(e);
            this.style.transform = 'scale(1.2)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 300);
        });
    });
    
    // Add click events to leaves
    document.querySelectorAll('.leaf').forEach(leaf => {
        leaf.addEventListener('click', function(e) {
            createLeafParticles(e);
            this.style.animation = 'leafSpin 1s ease-in-out';
        });
    });
    
    // Add click events to tea plants
    document.querySelectorAll('.plant').forEach(plant => {
        plant.addEventListener('click', function() {
            this.style.animation = 'plantDance 1s ease-in-out';
            setTimeout(() => {
                this.style.animation = 'plantSway 3s ease-in-out infinite';
            }, 1000);
        });
    });
    
    // Add click events to birds
    document.querySelectorAll('.bird').forEach(bird => {
        bird.addEventListener('click', function() {
            this.style.animation = 'birdFlutter 0.5s ease-in-out';
            setTimeout(() => {
                this.style.animation = 'birdFly 6s ease-in-out infinite';
            }, 500);
        });
    });
}

function showScene(sceneNumber) {
    // Hide all scenes
    document.querySelectorAll('.scene').forEach(scene => {
        scene.classList.remove('active');
    });
    
    // Show target scene
    const targetScene = document.getElementById(`scene${sceneNumber}`);
    if (targetScene) {
        setTimeout(() => {
            targetScene.classList.add('active');
        }, 100);
    }
    
    currentScene = sceneNumber;
    
    // Scene-specific animations
    setTimeout(() => {
        triggerSceneAnimations(sceneNumber);
    }, 500);
}

function triggerSceneAnimations(sceneNumber) {
    switch(sceneNumber) {
        case 0:
            animateFloatingBox();
            break;
        case 1:
            animateSchoolMemories();
            break;
        case 2:
            animateMatchaScene();
            break;
        case 3:
            animateDeclarationScene();
            break;
        case 4:
            animateClosingScene();
            break;
    }
}

function animateFloatingBox() {
    const box = document.getElementById('floatingBox');
    box.style.animation = 'floatBox 3s ease-in-out infinite, fadeInScale 1s ease-out';
}

function animateSchoolMemories() {
    const doors = document.querySelectorAll('.door');
    doors.forEach((door, index) => {
        setTimeout(() => {
            door.style.animation = 'doorOpen 2s ease-in-out';
        }, index * 500);
    });
    
    const memoryText = document.querySelector('.memory-text');
    setTimeout(() => {
        memoryText.style.animation = 'fadeInUp 2s ease-out';
    }, 1000);
}

function animateMatchaScene() {
    const character = document.querySelector('.ghibli-character');
    character.style.animation = 'characterAppear 2s ease-out, characterBreathe 2s ease-in-out infinite 2s';
    
    const matchaText = document.querySelector('.matcha-text');
    setTimeout(() => {
        matchaText.style.animation = 'fadeInUp 2s ease-out';
    }, 1500);
}

function animateDeclarationScene() {
    const letter = document.getElementById('flyingLetter');
    letter.style.animation = 'letterFlyIn 3s ease-out, letterFloat 4s ease-in-out infinite 3s';
    
    const goldenText = document.querySelector('.golden-text');
    setTimeout(() => {
        goldenText.style.animation = 'goldenAppear 2s ease-out, goldenGlow 3s ease-in-out infinite 2s';
    }, 2000);
}

function animateClosingScene() {
    const arumCharacter = document.querySelector('.arum-character');
    arumCharacter.style.animation = 'characterWalkIn 3s ease-out, walkAnimation 4s ease-in-out infinite 3s';
    
    const closingText = document.querySelector('.closing-text');
    setTimeout(() => {
        closingText.style.animation = 'fadeInUp 2s ease-out';
    }, 2000);
}

function nextScene() {
    if (currentScene < totalScenes - 1) {
        showScene(currentScene + 1);
    }
}

function previousScene() {
    if (currentScene > 0) {
        showScene(currentScene - 1);
    }
}

function restartStory() {
    showScene(0);
}

function toggleMusic() {
    const music = document.getElementById('backgroundMusic');
    const musicBtn = document.getElementById('musicToggle');
    
    if (musicPlaying) {
        music.pause();
        musicBtn.textContent = '🔇';
        musicPlaying = false;
    } else {
        music.play().catch(e => console.log('Audio play failed:', e));
        musicBtn.textContent = '🎵';
        musicPlaying = true;
    }
}

function toggleDayNight() {
    const body = document.body;
    const btn = document.getElementById('dayNightToggle');
    
    isNightMode = !isNightMode;
    
    if (isNightMode) {
        body.classList.add('night-mode');
        btn.textContent = '☀️';
    } else {
        body.classList.remove('night-mode');
        btn.textContent = '🌙';
    }
}

function createParticles() {
    const particleContainer = document.getElementById('particles');
    
    setInterval(() => {
        if (Math.random() < 0.3) { // 30% chance every interval
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
            particle.style.animationDelay = Math.random() * 2 + 's';
            
            particleContainer.appendChild(particle);
            
            // Remove particle after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 8000);
        }
    }, 1000);
}

function createStarParticles(event) {
    const rect = event.target.getBoundingClientRect();
    const x = rect.left + rect.width / 2;
    const y = rect.top + rect.height / 2;
    
    for (let i = 0; i < 8; i++) {
        const particle = document.createElement('div');
        particle.className = 'star-particle';
        particle.style.left = x + 'px';
        particle.style.top = y + 'px';
        particle.style.animationDelay = (i * 0.1) + 's';
        
        document.body.appendChild(particle);
        
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 2000);
    }
}

function createCloudParticles(event) {
    const rect = event.target.getBoundingClientRect();
    const x = rect.left + rect.width / 2;
    const y = rect.top + rect.height / 2;
    
    for (let i = 0; i < 5; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = x + 'px';
        particle.style.top = y + 'px';
        particle.style.position = 'fixed';
        particle.style.animationDuration = '2s';
        
        document.body.appendChild(particle);
        
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 2000);
    }
}

function createLeafParticles(event) {
    const rect = event.target.getBoundingClientRect();
    const x = rect.left + rect.width / 2;
    const y = rect.top + rect.height / 2;
    
    for (let i = 0; i < 3; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = x + 'px';
        particle.style.top = y + 'px';
        particle.style.position = 'fixed';
        particle.style.background = '#228B22';
        particle.style.animationDuration = '3s';
        
        document.body.appendChild(particle);
        
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 3000);
    }
}
