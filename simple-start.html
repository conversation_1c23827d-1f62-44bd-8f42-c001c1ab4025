<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aurum Splendet in Aeternum - Launcher</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.9);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            max-width: 600px;
            margin: 20px;
        }
        
        h1 {
            color: #8B4513;
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .subtitle {
            color: #D2691E;
            font-size: 1.2rem;
            margin-bottom: 30px;
            font-style: italic;
        }
        
        .launch-btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(145deg, #FFD700, #FFA500);
            color: #8B4513;
            text-decoration: none;
            border-radius: 25px;
            font-size: 1.2rem;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            margin: 10px;
        }
        
        .launch-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
        }
        
        .instructions {
            margin-top: 30px;
            text-align: left;
            color: #8B4513;
            line-height: 1.6;
        }
        
        .instructions h3 {
            color: #D2691E;
            margin-bottom: 10px;
        }
        
        .instructions ul {
            margin-left: 20px;
        }
        
        .note {
            background: #FFF8DC;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #FFD700;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Aurum Splendet in Aeternum</h1>
        <p class="subtitle">Emas yang Bersinar Abadi</p>
        
        <p>Sebuah aplikasi web interaktif dengan tema Studio Ghibli untuk menyatakan perasaan romantis kepada Arum.</p>
        
        <a href="index.html" class="launch-btn">🌟 Mulai Cerita Cinta</a>
        
        <div class="instructions">
            <h3>📱 Cara Menggunakan:</h3>
            <ul>
                <li><strong>Desktop:</strong> Gunakan tombol atau panah kiri/kanan untuk navigasi</li>
                <li><strong>Mobile:</strong> Tap tombol atau swipe untuk berpindah scene</li>
                <li><strong>Interaktif:</strong> Klik awan, daun, bintang, dan elemen lainnya</li>
                <li><strong>Audio:</strong> Gunakan tombol 🎵 untuk musik latar</li>
                <li><strong>Mode:</strong> Toggle 🌙/☀️ untuk mode siang/malam</li>
            </ul>
            
            <h3>🎵 Musik Latar:</h3>
            <p>Untuk pengalaman terbaik, tambahkan file musik piano lembut ke folder <code>audio/</code> dengan nama <code>ghibli-piano.mp3</code></p>
            
            <h3>🌟 Fitur Cerita:</h3>
            <ul>
                <li>5 scene romantis dengan animasi halus</li>
                <li>Kenangan SMA dan perasaan yang terpendam</li>
                <li>Elemen Yogyakarta dan budaya lokal</li>
                <li>Deklarasi cinta yang tulus dan bermakna</li>
                <li>Pesan harapan untuk masa depan</li>
            </ul>
        </div>
        
        <div class="note">
            <strong>💝 Catatan:</strong> Aplikasi ini dibuat dengan sepenuh hati untuk menyampaikan perasaan yang tulus. Semoga dapat menjadi cara yang indah untuk mengungkapkan cinta yang telah lama terpendam.
        </div>
    </div>
</body>
</html>
