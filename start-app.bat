@echo off
echo Starting Aurum Splendet in Aeternum...
echo Opening the romantic web application for Arum...
echo.

REM Try to start a local server for better audio support
echo Starting local web server...
start /min python -m http.server 8000

REM Wait a moment for server to start
timeout /t 2 /nobreak >nul

REM Open the application in default browser
echo Opening in browser...
start http://localhost:8000

echo.
echo Application started successfully!
echo.
echo Controls:
echo - Click buttons or use arrow keys to navigate
echo - Click interactive elements (clouds, leaves, stars)
echo - Use music and day/night toggle buttons
echo.
echo Press any key to close this window...
pause >nul
