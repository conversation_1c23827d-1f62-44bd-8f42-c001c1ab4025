# 💝 Cara Penggunaan: Aurum Splendet in Aeternum

## 🎯 Untuk Arum Savitri Chairunisa

Aplikasi web interaktif ini dibuat khusus untuk menyampaikan perasaan tulus yang telah lama terpendam sejak masa SMA. Setiap detail dirancang dengan penuh cinta dan harapan.

---

## 🚀 Cara Membuka Aplikasi

### Opsi 1: Langsung Buka (Termudah)
1. Buka file `index.html` dengan double-click
2. Atau buka `simple-start.html` untuk halaman launcher yang lebih user-friendly

### Opsi 2: Dengan Server Lokal (Untuk Audio Optimal)
1. Double-click file `start-app.bat`
2. Tunggu browser terbuka otomatis
3. Jika tidak terbuka, buka browser dan ketik: `http://localhost:8000`

### Opsi 3: Panduan Mobile
1. Buka `mobile-guide.html` untuk panduan lengkap penggunaan di ponsel
2. Kemudian lanjut ke aplikasi utama

---

## 📱 Pengguna<PERSON> di Mobile (Direkomendasikan)

### Navigasi:
- **<PERSON>p tombol "Lanjutkan"** untuk scene berikutnya
- **Swipe kiri** untuk maju
- **Swipe kanan** untuk mundur
- **Tap elemen interaktif** (awan, daun, bintang, dll)

### Kontrol:
- **🎵 Tombol Musik**: Pojok kanan atas untuk on/off musik
- **🌙 Mode Siang/Malam**: Toggle tema visual
- **Volume**: Pastikan ponsel tidak silent untuk mendengar musik

---

## 🎭 Alur Cerita (5 Scene)

### Scene 1: Pembukaan
- Langit senja dengan Gunung Merapi
- Kotak kayu melayang dengan pesan "Aurum Splendet in Aeternum"
- Dedikasi khusus untuk Arum Savitri Chairunisa

### Scene 2: Kenangan SMA  
- Visualisasi kelas MIA I (Arum) dan MIA III (Penulis)
- Pesan tentang ketertarikan sejak masa sekolah
- Daun berguguran yang bisa di-tap

### Scene 3: Matcha & Langit
- Kebun teh Yogyakarta dengan karakter Ghibli
- Metafora tentang kehangatan dan kenyamanan
- Pengakuan jatuh hati

### Scene 4: Deklarasi Perasaan
- Malam dengan Candi Borobudur
- Bintang-bintang yang bisa diklik
- Surat terbang berisi pengakuan lengkap
- Pesan tentang ketulusan dan keberanian

### Scene 5: Penutup & Harapan
- Arum berjalan di jalan Yogyakarta
- Burung-burung yang mengikuti
- Pesan tentang tekad dan harapan masa depan
- Doa agar tetap berteman meski ditolak

---

## ✨ Elemen Interaktif

Tap/klik elemen berikut untuk animasi khusus:
- ☁️ **Awan**: Partikel emas
- 🍃 **Daun**: Animasi berputar  
- ⭐ **Bintang**: Efek partikel bintang
- 🌱 **Tanaman**: Animasi menari
- 🐦 **Burung**: Animasi terbang

---

## 💌 Pesan yang Ingin Disampaikan

### Inti Perasaan:
1. **Ketertarikan sejak SMA** - Dari kelas MIA I dan MIA III
2. **Kekaguman pada senyum dan kepribadian** Arum
3. **Perasaan jatuh hati** yang tulus
4. **Keinginan mengenal lebih dekat** dan berproses menjadi lebih baik
5. **Keberanian menyatakan** daripada menyesal kemudian
6. **Tidak mengharapkan balasan** - hanya ingin Arum tahu
7. **Tekad berjuang** menjadi versi terbaik diri
8. **Harapan tetap berteman** meski mungkin ditolak

### Filosofi "Aurum Splendet in Aeternum":
- **Aurum** = Emas (panggilan untuk Arum)
- **Splendet** = Bersinar
- **In Aeternum** = Selamanya
- **Makna**: "Emas (Arum) yang bersinar selamanya dalam hati"

---

## 🎵 Musik & Atmosfer

- **File Audio**: `seasidetown.mp3` (sudah tersedia)
- **Tema Visual**: Studio Ghibli dengan elemen Yogyakarta
- **Warna**: Pastel dengan aksen emas
- **Suasana**: Romantis, nostalgik, dan penuh harapan

---

## 💡 Tips Penggunaan

1. **Baca dengan tenang** - Nikmati setiap scene
2. **Interaksi aktif** - Tap elemen-elemen di layar
3. **Dengan musik** - Hidupkan audio untuk pengalaman penuh
4. **Mode portrait** - Terbaik dibuka di ponsel posisi vertikal
5. **Tanpa terburu-buru** - Biarkan animasi berjalan natural

---

## 🌟 Harapan Pembuat

Semoga aplikasi ini dapat menjadi cara yang indah dan bermakna untuk menyampaikan perasaan yang telah lama terpendam. Tidak peduli bagaimana responsnya, yang terpenting adalah ketulusan telah disampaikan dengan cara yang istimewa.

**Untuk Arum**: Terima kasih telah menjadi inspirasi dan cahaya yang menerangi hari-hari sejak masa SMA. Apapun jawabanmu, kau akan selalu menjadi "Aurum" yang bersinar dalam kenangan indah.

---

*Dibuat dengan sepenuh hati dan harapan* ❤️  
*Aurum Splendet in Aeternum*
