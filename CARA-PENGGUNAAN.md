# 💝 Cara Penggunaan: Aurum Splendet in Aeternum

## 🎯 Untuk Arum Savitri Chairunisa

Aplikasi web interaktif ini dibuat khusus untuk menyampaikan perasaan tulus yang telah lama terpendam sejak masa SMA. Setiap detail dirancang dengan penuh cinta dan harapan.

---

## 🚀 Cara Membuka Aplikasi

### Opsi 1: Langsung Buka (Termudah)
1. Buka file `index.html` dengan double-click
2. Atau buka `simple-start.html` untuk halaman launcher yang lebih user-friendly

### Opsi 2: Dengan Server Lokal (Untuk Audio Optimal)
1. Double-click file `start-app.bat`
2. Tunggu browser terbuka otomatis
3. Jika tidak terbuka, buka browser dan ketik: `http://localhost:8000`

### Opsi 3: Panduan Mobile
1. Buka `mobile-guide.html` untuk panduan lengkap penggunaan di ponsel
2. Kemudian lanjut ke aplikasi utama

---

## 📱 Pengguna<PERSON> di Mobile (Direkomendasikan)

### Navigasi:
- **<PERSON>p tombol "Lanjutkan"** untuk scene berikutnya
- **Swipe kiri** untuk maju
- **Swipe kanan** untuk mundur
- **Tap elemen interaktif** (awan, daun, bintang, dll)

### Kontrol:
- **🎵 Tombol Musik**: Pojok kanan atas untuk on/off musik
- **🌙 Mode Siang/Malam**: Toggle tema visual
- **Volume**: Pastikan ponsel tidak silent untuk mendengar musik

---

## 🎭 Alur Cerita Lengkap (14 Scene) - Komik Bergerak

### **Bagian I: Pengakuan Perasaan**

#### Scene 0: Pembukaan
- Langit senja dengan Gunung Merapi
- Kotak kayu melayang dengan pesan "Aurum Splendet in Aeternum"
- "Untuk Sang Emas yang Bersinar di Langit Hidupku"

#### Scene 1: Kenangan SMA
- Visualisasi kelas MIA I (Arum) dan MIA III (Penulis)
- "Di antara koridor waktu yang memisahkan MIA I dan MIA III"
- Puisi tentang senyum yang mengubah jarak

#### Scene 2: Matcha & Langit
- Kebun teh Yogyakarta dengan karakter Ghibli
- Metafora matcha dan langit Jogja
- "Aku menyukaimu, Arum - lebih dalam dari akar teh"

#### Scene 3: Deklarasi Perasaan
- Malam dengan Candi Borobudur
- Surat terbang: "Aku menyukaimu - tiga kata yang kutulis dengan tinta keabadian"
- Bintang-bintang interaktif

#### Scene 4: Harapan Persahabatan
- "Jangan biarkan jarak mengubah kita menjadi dua orang asing"
- Metafora bayang-bayang penjaga
- "Karena kehilangan sahabat lebih pedih daripada patah hati"

#### Scene 5: Pesan Terakhir
- Hati-hati yang bisa diklik
- "Janganlah kita menjadi asing"
- Ucapan terima kasih yang tulus

### **Bagian II: Perjalanan Memantaskan Diri**

#### Scene 6: Bab I - Perjalanan Dimulai
- Jalan menuju gunung di kejauhan
- "Aku memulai perjalanan panjang memantaskan diri"
- "Bukan untuk mengubah takdir, tapi untuk menjadi versi terbaik"

#### Scene 7: Bab II - Menumbuhkan Akar
- Tumpukan buku dan pohon yang tumbuh
- "Aku belajar membaca buku-buku yang kau sukai"
- "Setiap halaman adalah jembatan menuju pemahamanmu"

#### Scene 8: Bab III - Membangun Karakter
- Cermin refleksi dan cahaya dalam
- "Aku belajar mendengarkan lebih dari berbicara"
- "Memahami bahwa cinta sejati adalah memberi tanpa mengharap"

#### Scene 9: Bab IV - Menghadapi Badai
- Awan badai dan kilat
- "Ada hari-hari ketika keraguan menghampiri"
- "Tapi kemudian aku ingat senyummu dan bangkit lagi"

#### Scene 10: Bab V - Menemukan Tujuan
- Kompas dan bintang utara
- "Perjalanan ini bukan hanya untukmu, tapi juga untuk diriku"
- "Untuk menjadi manusia yang utuh"

#### Scene 11: Bab VI - Menjadi Layak
- Aura emas dan simbol pencapaian
- "Perlahan, aku melihat perubahan dalam diriku"
- "Untuk menjadi sahabat terbaik yang bisa kau andalkan"

#### Scene 12: Bab VII - Janji Abadi
- Api abadi dan siluet pengawal
- "Aku akan menjadi pengawal yang setia"
- "Yang akan menjaga kebahagiaanmu dari kejauhan"

#### Scene 13: Epilog - Aurum Splendet in Aeternum
- Bintang-bintang tak terhingga
- "Cinta sejati bukan tentang memiliki, tapi tentang memberi"
- "Terima kasih telah menjadi alasan aku menjadi manusia yang lebih baik"

---

## ✨ Elemen Interaktif

Tap/klik elemen berikut untuk animasi khusus:
- ☁️ **Awan**: Partikel emas
- 🍃 **Daun**: Animasi berputar  
- ⭐ **Bintang**: Efek partikel bintang
- 🌱 **Tanaman**: Animasi menari
- 🐦 **Burung**: Animasi terbang

---

## 💌 Pesan yang Ingin Disampaikan

### Inti Perasaan:
1. **Ketertarikan sejak SMA** - "Dulu kau di MIA I, aku di MIA III"
2. **Kekaguman pada senyum** - "Senyummu selalu menyinari hari-hariku"
3. **Pengakuan sederhana** - "Aku suka sama kamu, Arum"
4. **Keberanian menyatakan** - "Lebih baik kunyatakan daripada menyesal"
5. **Tanpa beban** - "Tanpa jawaban pun tak mengapa"
6. **Lega setelah mengungkapkan** - "Dengan ini aku jadi merasa lega"
7. **Harapan terbesar** - "Janganlah kita menjadi asing"
8. **Persahabatan di atas segalanya** - "Menjadi pengawal yang mengantarkan tuan putri ke pangeran sejatinya"

### Filosofi "Aurum Splendet in Aeternum":
- **Aurum** = Emas (panggilan untuk Arum)
- **Splendet** = Bersinar
- **In Aeternum** = Selamanya
- **Makna**: "Emas (Arum) yang bersinar selamanya dalam hati"

---

## 🎵 Musik & Atmosfer

- **File Audio**: `seasidetown.mp3` (sudah tersedia)
- **Tema Visual**: Studio Ghibli dengan elemen Yogyakarta
- **Warna**: Pastel dengan aksen emas
- **Suasana**: Romantis, nostalgik, dan penuh harapan

---

## 💡 Tips Penggunaan

1. **Baca dengan tenang** - Nikmati setiap scene
2. **Interaksi aktif** - Tap elemen-elemen di layar
3. **Dengan musik** - Hidupkan audio untuk pengalaman penuh
4. **Mode portrait** - Terbaik dibuka di ponsel posisi vertikal
5. **Tanpa terburu-buru** - Biarkan animasi berjalan natural

---

## 🌟 Harapan Pembuat

Semoga aplikasi ini dapat menjadi cara yang indah dan bermakna untuk menyampaikan perasaan yang telah lama terpendam. Tidak peduli bagaimana responsnya, yang terpenting adalah ketulusan telah disampaikan dengan cara yang istimewa.

**Untuk Arum**: Terima kasih telah menjadi inspirasi dan cahaya yang menerangi hari-hari sejak masa SMA. Apapun jawabanmu, kau akan selalu menjadi "Aurum" yang bersinar dalam kenangan indah.

---

*Dibuat dengan sepenuh hati dan harapan* ❤️  
*Aurum Splendet in Aeternum*
