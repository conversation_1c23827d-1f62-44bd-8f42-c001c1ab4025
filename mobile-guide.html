<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Panduan Mobile - Aurum Splendet in Aeternum</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            line-height: 1.6;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            max-width: 100%;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #8B4513;
            font-size: 1.8rem;
            margin-bottom: 15px;
            text-align: center;
        }
        
        h2 {
            color: #D2691E;
            font-size: 1.3rem;
            margin: 20px 0 10px 0;
            border-bottom: 2px solid #FFD700;
            padding-bottom: 5px;
        }
        
        p, li {
            color: #2F4F4F;
            font-size: 1rem;
            line-height: 1.7;
            margin-bottom: 10px;
        }
        
        .highlight {
            background: #FFF8DC;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #FFD700;
            margin: 15px 0;
        }
        
        .button-demo {
            display: inline-block;
            padding: 12px 20px;
            background: linear-gradient(145deg, #FFD700, #FFA500);
            color: #8B4513;
            text-decoration: none;
            border-radius: 20px;
            font-weight: bold;
            margin: 10px 5px;
            text-align: center;
        }
        
        .emoji {
            font-size: 1.2rem;
            margin-right: 8px;
        }
        
        ul {
            padding-left: 20px;
        }
        
        .tip {
            background: #E8F5E8;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #32CD32;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 Panduan Penggunaan Mobile</h1>
        <p style="text-align: center; font-style: italic; color: #8B4513;">
            Aurum Splendet in Aeternum - Emas yang Bersinar Abadi
        </p>
    </div>

    <div class="container">
        <h2>🎯 Cara Navigasi</h2>
        <p><span class="emoji">👆</span><strong>Tap Tombol:</strong> Gunakan tombol "Lanjutkan" untuk berpindah ke scene berikutnya</p>
        <p><span class="emoji">👈</span><strong>Swipe Kiri:</strong> Geser layar ke kiri untuk scene berikutnya</p>
        <p><span class="emoji">👉</span><strong>Swipe Kanan:</strong> Geser layar ke kanan untuk kembali ke scene sebelumnya</p>
        
        <div class="tip">
            <strong>💡 Tips:</strong> Untuk pengalaman terbaik, pegang ponsel dalam posisi portrait (vertikal)
        </div>
    </div>

    <div class="container">
        <h2>🎵 Kontrol Audio & Visual</h2>
        <p><span class="emoji">🎵</span><strong>Musik:</strong> Tap tombol musik di pojok kanan atas untuk menghidupkan/mematikan musik latar</p>
        <p><span class="emoji">🌙</span><strong>Mode Siang/Malam:</strong> Tap tombol bulan/matahari untuk mengubah tema visual</p>
        
        <div class="highlight">
            <strong>🎼 Musik Latar:</strong> Aplikasi menggunakan file "seasidetown.mp3" yang sudah tersedia. Pastikan volume ponsel tidak dalam mode silent untuk mendengar musik.
        </div>
    </div>

    <div class="container">
        <h2>✨ Elemen Interaktif</h2>
        <p>Tap elemen-elemen berikut untuk melihat animasi khusus:</p>
        <ul>
            <li><span class="emoji">☁️</span><strong>Awan:</strong> Tap untuk membuat partikel emas</li>
            <li><span class="emoji">🍃</span><strong>Daun:</strong> Tap untuk animasi berputar</li>
            <li><span class="emoji">⭐</span><strong>Bintang:</strong> Tap untuk efek partikel bintang</li>
            <li><span class="emoji">🌱</span><strong>Tanaman:</strong> Tap untuk animasi menari</li>
            <li><span class="emoji">🐦</span><strong>Burung:</strong> Tap untuk animasi terbang</li>
        </ul>
    </div>

    <div class="container">
        <h2>📖 Alur Cerita</h2>
        <p><strong>Scene 1:</strong> Pembukaan dengan pesan cinta abadi</p>
        <p><strong>Scene 2:</strong> Kenangan masa SMA di kelas MIA I dan MIA III</p>
        <p><strong>Scene 3:</strong> Suasana kebun teh dengan karakter Ghibli</p>
        <p><strong>Scene 4:</strong> Deklarasi perasaan di bawah langit malam</p>
        <p><strong>Scene 5:</strong> Penutup dengan harapan dan doa</p>
    </div>

    <div class="container">
        <h2>💝 Pesan Khusus</h2>
        <div class="highlight">
            <p style="text-align: center; font-style: italic;">
                "Aplikasi ini dibuat dengan sepenuh hati untuk menyampaikan perasaan yang tulus kepada Arum Savitri Chairunisa. Setiap animasi, setiap kata, dan setiap detail dirancang untuk mengungkapkan cinta yang telah lama terpendam sejak masa SMA."
            </p>
        </div>
        
        <div class="tip">
            <strong>🌟 Saran:</strong> Nikmati setiap scene dengan tenang, baca setiap pesan dengan hati, dan rasakan ketulusan yang ingin disampaikan.
        </div>
    </div>

    <div class="container">
        <h2>🚀 Mulai Aplikasi</h2>
        <p style="text-align: center;">
            <a href="index.html" class="button-demo">
                🌟 Buka Aurum Splendet in Aeternum
            </a>
        </p>
        
        <p style="text-align: center; margin-top: 20px; font-size: 0.9rem; color: #696969;">
            Dibuat dengan ❤️ untuk Arum - Semoga pesan ini sampai ke hatimu
        </p>
    </div>
</body>
</html>
